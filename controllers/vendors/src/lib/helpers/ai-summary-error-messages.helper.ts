import type { QuestionnaireSummaryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

type AISummaryErrorCode = QuestionnaireSummaryResponseDto['errorCode'];

/**
 * Returns the appropriate error message for AI summary error codes.
 * Follows multiverse patterns for translations using function-based approach.
 */
export const getAISummaryErrorMessage = (
    errorCode: AISummaryErrorCode,
): string => {
    switch (errorCode) {
        case 'MIN_LIMIT': {
            return t`Please try a questionnaire with more than 450 words. Re-upload and try again.`;
        }
        case 'MAX_LIMIT': {
            return t`Please try a questionnaire with less than 5000 words. Re-upload and try again.`;
        }
        case 'INCORRECT_FORMAT': {
            return t`The questionnaire doesn't seem to be in a question and answer format. Re-upload the file in the right format.`;
        }
        case 'NO_RELEVANT_CONTENT': {
            return t`AI couldn't find any compliance, security or vendor information in this questionnaire. Re-upload and try again.`;
        }
        case 'API_FAIL': {
            return t`Something went wrong. Please try again.`;
        }
        case 'INVALID_SOC_2_FILE': {
            return t`AI couldn't find SOC 2 information in the report. Please try a different file.`;
        }
        case 'MAX_LIMIT_SOC': {
            return t`Please try a SOC 2 report with less than 650,000 characters.`;
        }
        default: {
            return t`An unknown error occurred.`;
        }
    }
};

/**
 * Returns the appropriate error title based on summary type.
 * Follows multiverse patterns for translations using function-based approach.
 */
export const getAISummaryErrorTitle = (
    summaryType: 'questionnaire' | 'soc',
): string => {
    switch (summaryType) {
        case 'soc': {
            return t`AI couldn't summarize this SOC 2 report`;
        }
        case 'questionnaire': {
            return t`AI couldn't summarize this questionnaire`;
        }
        default: {
            return t`AI couldn't summarize this document`;
        }
    }
};

/**
 * Determines if the error allows retry functionality.
 * Only API_FAIL errors should show retry button.
 */
export const canRetryAISummaryError = (
    errorCode: AISummaryErrorCode,
): boolean => {
    return errorCode === 'API_FAIL';
};
