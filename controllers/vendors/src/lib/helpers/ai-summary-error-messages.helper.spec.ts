import { describe, expect, test } from 'vitest';
import {
    canRetryAISummaryError,
    getAISummaryErrorMessage,
    getAISummaryErrorTitle,
} from './ai-summary-error-messages.helper';

describe('aI Summary Error Messages Helper', () => {
    describe('getAISummaryErrorMessage', () => {
        test('should return correct message for MIN_LIMIT error', () => {
            const result = getAISummaryErrorMessage('MIN_LIMIT');

            expect(result).toBe(
                'Please try a questionnaire with more than 450 words. Re-upload and try again.',
            );
        });

        test('should return correct message for MAX_LIMIT error', () => {
            const result = getAISummaryErrorMessage('MAX_LIMIT');

            expect(result).toBe(
                'Please try a questionnaire with less than 5000 words. Re-upload and try again.',
            );
        });

        test('should return correct message for INCORRECT_FORMAT error', () => {
            const result = getAISummaryErrorMessage('INCORRECT_FORMAT');

            expect(result).toBe(
                "The questionnaire doesn't seem to be in a question and answer format. Re-upload the file in the right format.",
            );
        });

        test('should return correct message for NO_RELEVANT_CONTENT error', () => {
            const result = getAISummaryErrorMessage('NO_RELEVANT_CONTENT');

            expect(result).toBe(
                "AI couldn't find any compliance, security or vendor information in this questionnaire. Re-upload and try again.",
            );
        });

        test('should return correct message for API_FAIL error', () => {
            const result = getAISummaryErrorMessage('API_FAIL');

            expect(result).toBe('Something went wrong. Please try again.');
        });

        test('should return correct message for INVALID_SOC_2_FILE error', () => {
            const result = getAISummaryErrorMessage('INVALID_SOC_2_FILE');

            expect(result).toBe(
                "AI couldn't find SOC 2 information in the report. Please try a different file.",
            );
        });

        test('should return correct message for MAX_LIMIT_SOC error', () => {
            const result = getAISummaryErrorMessage('MAX_LIMIT_SOC');

            expect(result).toBe(
                'Please try a SOC 2 report with less than 650,000 characters.',
            );
        });

        test('should return default message for undefined error', () => {
            const result = getAISummaryErrorMessage(undefined);

            expect(result).toBe('An unknown error occurred.');
        });
    });

    describe('getAISummaryErrorTitle', () => {
        test('should return correct title for questionnaire type', () => {
            const result = getAISummaryErrorTitle('questionnaire');

            expect(result).toBe("AI couldn't summarize this questionnaire");
        });

        test('should return correct title for soc type', () => {
            const result = getAISummaryErrorTitle('soc');

            expect(result).toBe("AI couldn't summarize this SOC 2 report");
        });
    });

    describe('canRetryAISummaryError', () => {
        test('should return true for API_FAIL error', () => {
            const result = canRetryAISummaryError('API_FAIL');

            expect(result).toBeTruthy();
        });

        test('should return false for MIN_LIMIT error', () => {
            const result = canRetryAISummaryError('MIN_LIMIT');

            expect(result).toBeFalsy();
        });

        test('should return false for MAX_LIMIT error', () => {
            const result = canRetryAISummaryError('MAX_LIMIT');

            expect(result).toBeFalsy();
        });

        test('should return false for INCORRECT_FORMAT error', () => {
            const result = canRetryAISummaryError('INCORRECT_FORMAT');

            expect(result).toBeFalsy();
        });

        test('should return false for NO_RELEVANT_CONTENT error', () => {
            const result = canRetryAISummaryError('NO_RELEVANT_CONTENT');

            expect(result).toBeFalsy();
        });

        test('should return false for INVALID_SOC_2_FILE error', () => {
            const result = canRetryAISummaryError('INVALID_SOC_2_FILE');

            expect(result).toBeFalsy();
        });

        test('should return false for MAX_LIMIT_SOC error', () => {
            const result = canRetryAISummaryError('MAX_LIMIT_SOC');

            expect(result).toBeFalsy();
        });

        test('should return false for undefined error', () => {
            const result = canRetryAISummaryError(undefined);

            expect(result).toBeFalsy();
        });
    });
});
