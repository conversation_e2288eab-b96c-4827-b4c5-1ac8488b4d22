import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    questionnairesControllerSaveQuestionnaireFeedbackMutation,
    summariesControllerSaveSummaryFeedbackMutation,
} from '@globals/api-sdk/queries';
import type { SummaryFeedbackRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

export interface FeedbackFormData {
    feedbackTypes: SummaryFeedbackRequestDto['feedbackTypes'];
    feedback: SummaryFeedbackRequestDto['feedback'];
}

class VendorsAISummaryFeedbackController {
    constructor() {
        makeAutoObservable(this);
    }

    saveSummaryFeedbackMutation = new ObservedMutation(
        summariesControllerSaveSummaryFeedbackMutation,
    );

    saveQuestionnaireFeedbackMutation = new ObservedMutation(
        questionnairesControllerSaveQuestionnaireFeedbackMutation,
    );

    get isSubmittingFeedback(): boolean {
        return (
            this.saveSummaryFeedbackMutation.isPending ||
            this.saveQuestionnaireFeedbackMutation.isPending
        );
    }

    submitPositiveFeedback = (
        executionId: string,
        summaryType: 'questionnaire' | 'soc' | null,
        questionnaireId?: number,
    ): void => {
        if (!summaryType) {
            return;
        }
        const isQuestionnaire =
            summaryType === 'questionnaire' && questionnaireId;

        if (isQuestionnaire) {
            this.saveQuestionnaireFeedbackMutation.mutate({
                path: { id: questionnaireId },
                body: {
                    executionId,
                    feedbackStatusType: 'USEFUL',
                },
            });
        } else {
            this.saveSummaryFeedbackMutation.mutate({
                body: {
                    executionId,
                    feedbackStatusType: 'USEFUL',
                },
            });
        }

        when(
            () => !this.isSubmittingFeedback,
            () => {
                if (
                    (isQuestionnaire &&
                        this.saveQuestionnaireFeedbackMutation.hasError) ||
                    (!isQuestionnaire &&
                        this.saveSummaryFeedbackMutation.hasError)
                ) {
                    snackbarController.addSnackbar({
                        id: 'ai-summary-feedback-error',
                        props: {
                            title: t`Something went wrong`,
                            description: t`Unable to send feedback at this time.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }
                snackbarController.addSnackbar({
                    id: 'ai-summary-feedback-success',
                    props: {
                        title: t`Success`,
                        description: t`Thank you for the feedback.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    submitNegativeFeedback = async (
        executionId: string,
        summaryType: 'questionnaire' | 'soc' | null,
        formData: FeedbackFormData,
        questionnaireId?: number,
    ): Promise<void> => {
        if (!summaryType) {
            return;
        }

        try {
            const feedbackTypes = isEmpty(formData.feedbackTypes)
                ? null
                : formData.feedbackTypes;
            const { feedback } = formData;

            if (summaryType === 'questionnaire' && questionnaireId) {
                await this.saveQuestionnaireFeedbackMutation.mutateAsync({
                    path: { id: questionnaireId },
                    body: {
                        executionId,
                        feedbackStatusType: 'NOT_USEFUL',
                        feedbackTypes,
                        feedback,
                    },
                });
            } else {
                await this.saveSummaryFeedbackMutation.mutateAsync({
                    body: {
                        executionId,
                        feedbackStatusType: 'NOT_USEFUL',
                        feedbackTypes,
                        feedback,
                    },
                });
            }

            snackbarController.addSnackbar({
                id: 'ai-summary-negative-feedback-success',
                props: {
                    title: t`Success`,
                    description: t`Thank you for the feedback.`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } catch {
            snackbarController.addSnackbar({
                id: 'ai-summary-negative-feedback-error',
                props: {
                    title: t`Something went wrong`,
                    description: t`Unable to send feedback at this time.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };
}

export const sharedVendorsAISummaryFeedbackController =
    new VendorsAISummaryFeedbackController();
