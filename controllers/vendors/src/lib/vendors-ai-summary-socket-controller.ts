import { isError } from 'lodash-es';
import { SocketEvent } from '@drata/enums';
import { logger } from '@globals/logger';
import { makeAutoObservable, reaction, runInAction } from '@globals/mobx';
import { sharedSocketController } from '@globals/socket';
import type {
    SocketCallbackData,
    SummaryEventCallback,
} from '../types/ai-summary-socket.types';

export class VendorsAISummarySocketController {
    isInitialized = false;

    questionnaireCallback: SummaryEventCallback | null = null;
    socCallback: SummaryEventCallback | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    init(): void {
        if (this.isInitialized) {
            return;
        }

        reaction(
            () => sharedSocketController.isInitialized,
            (ready) => {
                if (!ready || this.isInitialized) {
                    return;
                }

                this.subscribeToAISummaryEvents();
                runInAction(() => {
                    this.isInitialized = true;
                });
            },
            { fireImmediately: true },
        );
    }

    subscribeToAISummaryEvents(): void {
        // Subscribe to questionnaire summary completion events
        sharedSocketController.subscribe({
            channelType: 'company',
            eventName: SocketEvent.QUESTIONNAIRE_SUMMARY_COMPLETED,
            callback: this.handleQuestionnaireSummaryCompleted,
        });

        // Subscribe to SOC report summary completion events
        sharedSocketController.subscribe({
            channelType: 'company',
            eventName: SocketEvent.SUMMARY_GENERATED,
            callback: this.handleSocSummaryGenerated,
        });
    }

    handleQuestionnaireSummaryCompleted = (data: SocketCallbackData): void => {
        // Call the registered questionnaire callback
        if (this.questionnaireCallback) {
            try {
                this.questionnaireCallback(data);
            } catch (error) {
                logger.error({
                    message: 'Error in questionnaire summary socket callback',
                    errorObject: {
                        message: isError(error) ? error.message : String(error),
                        statusCode: '500',
                    },
                });
            }
        }
    };

    handleSocSummaryGenerated = (data: SocketCallbackData): void => {
        // Call the registered SOC callback
        if (this.socCallback) {
            try {
                this.socCallback(data);
            } catch (error) {
                logger.error({
                    message: 'Error in SOC summary socket callback',
                    errorObject: {
                        message: isError(error) ? error.message : String(error),
                        statusCode: '500',
                    },
                });
            }
        }
    };

    /**
     * Register a callback for questionnaire summary completion events.
     * Only one callback is allowed at a time.
     */
    onQuestionnaireSummaryCompleted(
        callback: SummaryEventCallback,
    ): () => void {
        this.questionnaireCallback = callback;

        /**
         * Return unsubscribe function.
         */
        return () => {
            this.questionnaireCallback = null;
        };
    }

    /**
     * Register a callback for SOC summary completion events.
     * Only one callback is allowed at a time.
     */
    onSocSummaryGenerated(callback: SummaryEventCallback): () => void {
        this.socCallback = callback;

        /**
         * Return unsubscribe function.
         */
        return () => {
            this.socCallback = null;
        };
    }
}

export const sharedVendorsAISummarySocketController =
    new VendorsAISummarySocketController();
