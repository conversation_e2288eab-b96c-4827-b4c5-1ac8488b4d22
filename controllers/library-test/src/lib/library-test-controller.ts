import { isNil } from 'lodash-es';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    libraryTestTemplateControllerGetTestTemplateActiveTestsOptions,
    libraryTestTemplateControllerGetTestTemplateDetailsOptions,
    libraryTestTemplateControllerGetTestTemplateMappingsOptions,
    libraryTestTemplateControllerValidateImportTestTemplateMutation,
} from '@globals/api-sdk/queries';
import type {
    ControlTestInstanceBaseResponseDto,
    LibraryTestTemplateDetailsResponseDto,
    LibraryTestTemplateMappedControlDto,
    LibraryTestTemplateMappingsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    LIBRARY_TEST_MAPPING_FRAMEWORK_FILTER_ID,
    LIBRARY_TEST_MAPPING_NAME_ID,
} from '@views/library-test-mappings';
import type { Recipe } from '../../../../components/library-test-logic-card/src/lib/types/recipe.type';

class LibraryTestController {
    mappedControlsPagination = {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    };

    activeTestsPagination = {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    };

    filters: { frameworkTemplateIds: number[] } = {
        frameworkTemplateIds: [],
    };

    testQuery = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateDetailsOptions,
    );

    activeTestsQuery = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateActiveTestsOptions,
    );

    getTestTemplateControls = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateMappingsOptions,
    );

    validateTestTemplateMutation = new ObservedMutation(
        libraryTestTemplateControllerValidateImportTestTemplateMutation,
    );

    get isLoading(): boolean {
        return this.testQuery.isLoading;
    }

    get error(): Error | null {
        return this.testQuery.error;
    }

    get isLoadingTestTemplateControls(): boolean {
        return this.getTestTemplateControls.isLoading;
    }

    constructor() {
        makeAutoObservable(this);
    }

    get testTemplateControls(): LibraryTestTemplateMappingsResponseDto[] {
        return this.getTestTemplateControls.data?.data ?? [];
    }

    get totalMonitorControls(): number {
        return this.getTestTemplateControls.data?.total ?? 0;
    }

    get monitoringControlInstance(): LibraryTestTemplateDetailsResponseDto | null {
        return this.testQuery.data;
    }

    get connectionClientTypeLabels(): string[] {
        return (
            this.monitoringControlInstance?.connectionClientTypes.map(
                ({ displayName }) => displayName,
            ) ?? []
        );
    }

    get connectionClientTypeValues(): string[] {
        return (
            this.monitoringControlInstance?.connectionClientTypes.map(
                ({ value }) => value,
            ) ?? []
        );
    }

    get isAp2Test(): boolean {
        return !isNil(this.monitoringControlInstance?.ap2EnabledAt);
    }

    get testName(): string {
        return this.monitoringControlInstance?.name ?? t`Test`;
    }

    get id(): number | null {
        return this.monitoringControlInstance?.id ?? null;
    }

    get isAiGenerated(): boolean {
        return (
            !isNil(this.monitoringControlInstance) &&
            ['DRATA_LIBRARY'].includes(this.monitoringControlInstance.source)
        );
    }

    get remedyDescription(): string {
        return this.monitoringControlInstance?.remedyDescription ?? '';
    }

    /**
     * Returns the controls associated with the current test.
     * This data is available in the test details response without requiring a separate API call.
     */
    get testControls(): LibraryTestTemplateMappedControlDto[] {
        if (isNil(this.monitoringControlInstance)) {
            return [];
        }

        return this.monitoringControlInstance.controls;
    }

    /**
     * Returns the Active Tests for the current test in all tenants.
     */
    get activeTests(): ControlTestInstanceBaseResponseDto[] {
        if (isNil(this.monitoringControlInstance)) {
            return [];
        }

        if (isNil(this.activeTestsQuery.data?.data)) {
            return [];
        }

        return this.activeTestsQuery.data.data;
    }

    get activeTestsTotal(): number {
        return this.activeTestsQuery.data?.total ?? 0;
    }

    get activeTestsIsLoading(): boolean {
        return this.activeTestsQuery.isLoading;
    }

    get templateHasErrors(): boolean {
        return !this.validateTestTemplateMutation.response?.isValid;
    }

    get templateValidationIsLoading(): boolean {
        return this.validateTestTemplateMutation.isPending;
    }

    validateTestTemplate = (testId: number): void => {
        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                if (isNil(testId)) {
                    logger.error({
                        message:
                            'Failed to validate test template. Missing test ID',
                        additionalInfo: {
                            testId,
                        },
                    });

                    return;
                }
                this.validateTestTemplateMutation.mutate({
                    path: { testId },
                });
            },
        );
    };
    /**
     * Returns the TestLogic for the overview test.
     */
    get recipe() {
        if (!this.monitoringControlInstance?.recipe) {
            return null;
        }

        try {
            return JSON.parse(this.monitoringControlInstance.recipe) as Recipe;
        } catch {
            return null;
        }
    }

    loadTest = (testId: number) => {
        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                this.testQuery.load({
                    path: { templateId: testId },
                });
                this.activeTestsQuery.load({
                    path: { templateId: testId },
                    query: {
                        ...this.activeTestsPagination,
                        limit: this.activeTestsPagination.pageSize,
                    },
                });
                this.getTestTemplateControls.load({
                    path: { templateId: testId },
                    query: {
                        ...this.mappedControlsPagination,
                        ...this.filters,
                        limit: this.mappedControlsPagination.pageSize,
                    },
                });
            },
        );
    };

    loadTestTemplateControls = (params?: FetchDataResponseParams): void => {
        const testId = this.testQuery.data?.testId;

        if (isNil(testId)) {
            return;
        }
        if (params) {
            this.mappedControlsPagination = {
                page: params.pagination.page || 1,
                pageSize: params.pagination.pageSize || DEFAULT_PAGE_SIZE,
                pageIndex: params.pagination.pageIndex || 0,
                pageSizeOptions: params.pagination.pageSizeOptions,
            };
            const frameworksFilterValues = params.globalFilter.filters[
                LIBRARY_TEST_MAPPING_FRAMEWORK_FILTER_ID
            ].value as { value: string }[] | undefined;

            this.filters.frameworkTemplateIds =
                frameworksFilterValues?.map((item) => Number(item.value)) ?? [];
        }
        const sortItem = params?.sorting[0];
        const sortByName =
            sortItem?.id === LIBRARY_TEST_MAPPING_NAME_ID
                ? LIBRARY_TEST_MAPPING_NAME_ID
                : undefined;

        this.getTestTemplateControls.load({
            path: { templateId: testId },
            query: {
                ...this.mappedControlsPagination,
                ...this.filters,
                limit: this.mappedControlsPagination.pageSize,
                sort: sortByName,
                sortDir: sortItem?.desc ? 'DESC' : 'ASC',
            },
        });
    };

    loadActiveTests = (params?: FetchDataResponseParams): void => {
        const testId = this.testQuery.data?.testId;

        if (isNil(testId)) {
            return;
        }
        if (params) {
            this.activeTestsPagination = {
                page: params.pagination.page || 1,
                pageSize: params.pagination.pageSize || DEFAULT_PAGE_SIZE,
                pageIndex: params.pagination.pageIndex || 0,
                pageSizeOptions: params.pagination.pageSizeOptions,
            };
        }
        this.activeTestsQuery.load({
            path: { templateId: testId },
            query: {
                ...this.activeTestsPagination,
                limit: this.activeTestsPagination.pageSize,
            },
        });
    };
}

export const activeLibraryTestController = new LibraryTestController();
