import type { ClientLoader } from '@app/types';
import { sharedUtilitiesObservationsController } from '@controllers/utilities';
import {
    sharedVendorsSecurityReviewFileController,
    sharedVendorsSecurityReviewObservationsController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { SecurityReviewFilePageHeaderModel } from '@models/vendor-security-reviews';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewFilesView } from '@views/vendors-profile-security-review-files';

export const meta: MetaFunction = () => [
    { title: t`Vendors Current Security Review File` },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { securityReviewId, fileId, vendorId } = params;

        if (!securityReviewId || isNaN(Number(securityReviewId))) {
            throw new Error('Invalid securityReviewId');
        }

        if (!fileId || isNaN(Number(fileId))) {
            throw new Error('Invalid fileId');
        }

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsSecurityReviewFileController.setVendorId(Number(vendorId));

        sharedVendorsSecurityReviewFileController.setSecurityReviewId(
            Number(securityReviewId),
        );

        sharedVendorsSecurityReviewFileController.loadPdfDownloadUrl(
            Number(fileId),
        );

        sharedVendorsSecurityReviewFileController.loadSecurityReviewDocument(
            Number(fileId),
        );

        sharedVendorsSecurityReviewFileController.setVendorType('current');

        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        sharedUtilitiesObservationsController.openUtility();

        return {
            pageHeader: new SecurityReviewFilePageHeaderModel('current'),
            contentNav: {
                tabs: [],
            },
            utilities: {
                utilitiesList: ['observations'],
            },
        };
    },
);

const VendorsCurrentSecurityReviewFile = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewFilesView
            data-testid="VendorsCurrentSecurityReviewFile"
            data-id="TsaCYRBa"
            vendorType="current"
        />
    );
};

export default VendorsCurrentSecurityReviewFile;
