import { isEmpty } from 'lodash-es';
import { activeLibraryTestController } from '@controllers/library-test';
import { Card } from '@cosmos/components/card';
import { Datatable } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { LibraryTestActiveTestsColumns } from '../types/library-test-active-tests-columns.types';
import { LIBRARY_TEST_ACTIVE_TESTS_COLUMNS } from './cells/library-test-active-tests-columns.constants';

export const LibraryTestActiveTestsCardComponent = observer(
    (): React.JSX.Element => {
        const { isMultipleWorkspacesEnabled } = sharedFeatureAccessModel;

        const columns = LIBRARY_TEST_ACTIVE_TESTS_COLUMNS.filter(
            (column) =>
                isMultipleWorkspacesEnabled || column.id !== 'workspace',
        );

        return (
            <Card
                title={t`Active tests`}
                data-testid="LibraryTestActiveTestsCardComponent"
                data-id="EDpb1chT"
                body={
                    isEmpty(activeLibraryTestController.activeTests) &&
                    !activeLibraryTestController.activeTestsIsLoading ? (
                        <Stack
                            align="center"
                            data-testid="LibraryTestActiveTestsTestNameCell"
                            height="100%"
                        >
                            <Text type="body">
                                <Trans>No active tests at this time</Trans>
                            </Text>
                        </Stack>
                    ) : (
                        <Datatable
                            isSortable={false}
                            tableId="library-test-active-tests-datatable"
                            columns={columns}
                            data-id="library-test-active-tests-datatable"
                            total={activeLibraryTestController.activeTestsTotal}
                            isLoading={
                                activeLibraryTestController.activeTestsIsLoading
                            }
                            data={activeLibraryTestController.activeTests.flatMap(
                                (test) =>
                                    test.products.map(
                                        (product) =>
                                            ({
                                                name: test.name,
                                                workspace: product.name,
                                                workspaceId: product.id,
                                                testId: test.testId,
                                            }) as LibraryTestActiveTestsColumns,
                                    ),
                            )}
                            tableSearchProps={{
                                hideSearch: true,
                            }}
                            onFetchData={
                                activeLibraryTestController.loadActiveTests
                            }
                        />
                    )
                }
            />
        );
    },
);
