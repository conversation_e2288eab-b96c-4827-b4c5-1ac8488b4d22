import { isEmpty, isObject, isString, uniqueId } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { List } from '@cosmos-lab/components/list';
import { getSecurityReviewAiSummaryMessage } from './helpers/get-security-review-ai-summary-message.helper';
import { getSummarySectionTitle } from './helpers/summary-section-title.helper';
import type { SectionListProps } from './types/summary-section-title.type';

export const VendorSecurityReviewsAISummarySectionsListComponent = ({
    summarySection,
    summarySelectedType,
}: SectionListProps): React.JSX.Element => {
    return (
        <>
            <Text type="title" size="200">
                {getSummarySectionTitle({
                    summarySection,
                    summarySelectedType,
                })}
            </Text>
            {Array.isArray(summarySection.values) &&
                !isEmpty(summarySection.values) && (
                    <List
                        items={summarySection.values.map((item) => (
                            <Text
                                data-id="fYLMtpAz"
                                key={uniqueId(
                                    isObject(item) ? item.title : item,
                                )}
                            >
                                {getSecurityReviewAiSummaryMessage(item)}
                            </Text>
                        ))}
                    />
                )}
            {isString(summarySection.values) && (
                <List
                    items={[
                        <Text key={uniqueId(summarySection.values)}>
                            {summarySection.values}
                        </Text>,
                    ]}
                />
            )}
            <Divider />
        </>
    );
};
