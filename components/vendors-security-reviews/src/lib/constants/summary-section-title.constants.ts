import { t } from '@globals/i18n/macro';
import type { SummarySectionValue } from '../types/summary-section-title.type';

export function getSectionTitle(section: SummarySectionValue): string {
    switch (section) {
        case 'overview': {
            return t`Overview`;
        }
        case 'exception': {
            return t`Exceptions found`;
        }
        case 'managementResponse': {
            return t`Management's response`;
        }
        default: {
            return '';
        }
    }
}
