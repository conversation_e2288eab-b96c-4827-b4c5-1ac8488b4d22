import { isString } from 'lodash-es';
import type { default as React } from 'react';
import {
    type FeedbackFormData,
    sharedVendorsAISummaryFeedbackController,
    sharedVendorsProfileQuestionnaireAISummaryController,
} from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { Form, useFormSubmit } from '@ui/forms';
import { closeAISummaryFeedbackModal } from './helpers/open-ai-summary-feedback-modal.helper';
import { buildAISummaryFeedbackFormSchema } from './schemas/ai-summary-feedback-form.schema';

interface AISummaryFeedbackModalProps {
    'data-id'?: string;
}

export const VendorsSecurityReviewsAISummaryFeedbackModal = observer(
    ({ 'data-id': dataId }: AISummaryFeedbackModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleSubmitFeedback = action(
            (values: Record<string, unknown>) => {
                const feedbackTypes: (
                    | 'NOT_ACCURATE'
                    | 'NOT_HELPFUL'
                    | 'OTHER'
                )[] = [];

                if (values.feedbackNotAccurate) {
                    feedbackTypes.push('NOT_ACCURATE');
                }
                if (values.feedbackNotHelpful) {
                    feedbackTypes.push('NOT_HELPFUL');
                }
                if (values.feedbackOther) {
                    feedbackTypes.push('OTHER');
                }

                const feedback = isString(values.feedbackText)
                    ? values.feedbackText.trim()
                    : null;

                const formData: FeedbackFormData = {
                    feedbackTypes,
                    feedback,
                };

                const { submitNegativeFeedback, isSubmittingFeedback } =
                    sharedVendorsAISummaryFeedbackController;
                const { executionId, summaryType, questionnaireId } =
                    sharedVendorsProfileQuestionnaireAISummaryController;

                if (isSubmittingFeedback || !executionId) {
                    return;
                }

                submitNegativeFeedback(
                    executionId,
                    summaryType,
                    formData,
                    questionnaireId || undefined,
                ).then(() => {
                    closeAISummaryFeedbackModal();
                });
            },
        );

        return (
            <>
                <Modal.Header
                    title={t`Help us improve`}
                    closeButtonAriaLabel={t`Close feedback modal`}
                    onClose={closeAISummaryFeedbackModal}
                />
                <Modal.Body>
                    <Stack gap="lg" direction="column">
                        <Text type="body" size="200">
                            {t`What best describes the problem with this questionnaire summary?`}
                        </Text>

                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId="ai-feedback-form"
                            schema={buildAISummaryFeedbackFormSchema()}
                            data-id={dataId || 'ai-feedback-form'}
                            onSubmit={handleSubmitFeedback}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Send feedback`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit();
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
