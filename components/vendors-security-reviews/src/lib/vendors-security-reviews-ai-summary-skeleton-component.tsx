import { uniqueId } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { SUMMARY_SKELETON_WIDTH_SIZES } from './constants/summary-skeleton.constants';

export const VendorsSecurityReviewsAISummarySkeletonComponent =
    (): React.JSX.Element => {
        return (
            <Box
                p="6x"
                borderWidth="borderWidth1"
                borderColor="neutralBorderFaded"
                borderRadius="borderRadiusLg"
                backgroundColor="neutralBackgroundSurfaceInitial"
                data-id="security-review-file-ai-summary-skeleton"
                data-testid="VendorsSecurityReviewsAISummarySkeletonComponent"
            >
                <Stack
                    direction="column"
                    gap="xs"
                    data-testid="summary-skeleton"
                    data-id="ai-summary-skeleton"
                >
                    {SUMMARY_SKELETON_WIDTH_SIZES.map(({ width }) => (
                        <Skeleton
                            key={`skeleton-bar-${width}-${uniqueId()}`}
                            data-testid={'skeleton-bar-width'}
                            barHeight="16px"
                            width={width}
                            barCount={1}
                            data-id={'ai-summary-skeleton-bar-width'}
                        />
                    ))}
                </Stack>
            </Box>
        );
    };
