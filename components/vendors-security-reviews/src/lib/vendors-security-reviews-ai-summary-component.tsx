import { isEmpty, isNil } from 'lodash-es';
import { sharedVendorsProfileQuestionnaireAISummaryController } from '@controllers/vendors';
import { Accordion } from '@cosmos/components/accordion';
import { Loader } from '@cosmos/components/loader';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { copyAISummaryToClipboard } from './helpers/copy-ai-summary.helper';
import { VendorSecurityReviewsAISummaryErrorComponent } from './vendors-security-reviews-ai-summary-error-component';
import { VendorsSecurityReviewsAISummaryFeedbackComponent } from './vendors-security-reviews-ai-summary-feedback-component';
import { VendorSecurityReviewsAISummarySectionsListComponent } from './vendors-security-reviews-ai-summary-sections-list-component';
import { VendorsSecurityReviewsAISummarySkeletonComponent } from './vendors-security-reviews-ai-summary-skeleton-component';

export const VendorSecurityReviewsAISummaryComponent = observer(
    (): React.JSX.Element => {
        const {
            summary,
            isLoading,
            isSaving,
            shouldShowSpinner,
            shouldShowSkeleton,
            summarySelectedType,
            summaryError,
        } = sharedVendorsProfileQuestionnaireAISummaryController;

        if (shouldShowSkeleton) {
            return <VendorsSecurityReviewsAISummarySkeletonComponent />;
        }

        if (shouldShowSpinner || isLoading || isSaving) {
            return <Loader isSpinnerOnly size="md" label={t`Loading`} />;
        }

        if (summaryError) {
            return <VendorSecurityReviewsAISummaryErrorComponent />;
        }

        return (
            <>
                {!isNil(summary) && !isEmpty(summary) && (
                    <Accordion
                        data-id="security-review-file-ai-summary"
                        data-testid="VendorSecurityReviewsAISummaryComponent"
                        supportingContent={
                            <Metadata label={t`Beta`} type="tag" />
                        }
                        title={
                            summarySelectedType === 'questionnaire'
                                ? t`Questionnaire summary`
                                : t`Report summary`
                        }
                        iconSlot={{
                            slotType: 'icon',
                            typeProps: {
                                name: 'AI',
                                colorScheme: 'neutral',
                            },
                        }}
                        body={
                            <Stack gap="lg" direction="column">
                                {summary.map((summarySection) => {
                                    return (
                                        <VendorSecurityReviewsAISummarySectionsListComponent
                                            key={summarySection.title}
                                            summarySection={summarySection}
                                            data-id="7rZrw42J"
                                            summarySelectedType={
                                                summarySelectedType
                                            }
                                        />
                                    );
                                })}

                                <VendorsSecurityReviewsAISummaryFeedbackComponent
                                    onCopy={() => {
                                        copyAISummaryToClipboard(summary);
                                    }}
                                />
                            </Stack>
                        }
                    />
                )}
            </>
        );
    },
);
