import {
    sharedVendorsAISummaryFeedbackController,
    sharedVendorsProfileQuestionnaireAISummaryController,
} from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { AIToolbar } from '@cosmos-lab/components/ai-toolbar';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { openAISummaryFeedbackModal } from './helpers/open-ai-summary-feedback-modal.helper';

interface Props {
    onCopy?: () => void;
}

export const VendorsSecurityReviewsAISummaryFeedbackComponent = observer(
    ({ onCopy }: Props): React.JSX.Element => {
        const isReadOnly = sharedFeatureAccessModel.isVendorAccessReadOnly;
        const { executionId, summaryType, questionnaireId } =
            sharedVendorsProfileQuestionnaireAISummaryController;
        const { isSubmittingFeedback, submitPositiveFeedback } =
            sharedVendorsAISummaryFeedbackController;

        const handleLike = () => {
            if (!executionId || isReadOnly || isSubmittingFeedback) {
                return;
            }

            submitPositiveFeedback(
                executionId,
                summaryType,
                questionnaireId || undefined,
            );
        };

        const handleDislike = () => {
            if (isReadOnly || isSubmittingFeedback) {
                return;
            }

            openAISummaryFeedbackModal();
        };

        return (
            <Stack justify="between" align="center" data-id="VFM8oDnu">
                <AIToolbar
                    data-id="vendors-security-reviews-ai-summary-feedback-toolbar"
                    viewDetailsContent={
                        <AppLink
                            isExternal
                            href="https://drata.com/blog/our-ai-philosophy"
                            data-id="ai-summary-learn-more-link"
                            data-testid="ai-summary-learn-more-link"
                        >
                            {t`Learn more about AI summaries`}
                        </AppLink>
                    }
                    onCopy={isReadOnly ? undefined : onCopy}
                    onThumbsUp={
                        isReadOnly || isSubmittingFeedback || !executionId
                            ? undefined
                            : handleLike
                    }
                    onThumbsDown={
                        isReadOnly || isSubmittingFeedback || !executionId
                            ? undefined
                            : handleDislike
                    }
                />
            </Stack>
        );
    },
);
