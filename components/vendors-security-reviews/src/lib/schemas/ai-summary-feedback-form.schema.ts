import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

export const buildAISummaryFeedbackFormSchema = (): FormSchema => ({
    feedbackNotAccurate: {
        type: 'checkbox',
        label: t`This is not accurate`,
        initialValue: false,
    },
    feedbackNotHelpful: {
        type: 'checkbox',
        label: t`This does not add value`,
        initialValue: false,
    },
    feedbackOther: {
        type: 'checkbox',
        label: t`Other`,
        initialValue: false,
    },
    feedbackText: {
        type: 'textarea',
        label: t`Feedback`,
        maxCharacters: 30000,
        rows: 3,
        isOptional: true,
        shownIf: {
            fieldName: 'feedbackOther',
            operator: 'equals',
            value: true,
        },
        validator: z
            .string({ required_error: t`Feedback is required` })
            .max(30000, {
                message: t`Maximum 30000 characters allowed`,
            }),
    },
});
