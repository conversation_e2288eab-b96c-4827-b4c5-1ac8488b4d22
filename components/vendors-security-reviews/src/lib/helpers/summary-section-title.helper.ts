import { plural } from '@globals/i18n/macro';
import { getSectionTitle } from '../constants/summary-section-title.constants';
import type {
    SectionListProps,
    SummarySectionValue,
} from '../types/summary-section-title.type';

export const getSummarySectionTitle = ({
    summarySection,
    summarySelectedType,
}: SectionListProps): string => {
    const { title, values } = summarySection;

    if (summarySelectedType === 'soc') {
        if (title === 'exception') {
            return plural(values.length, {
                one: '# Exception found',
                other: '# Exceptions found',
            });
        }

        return getSectionTitle(title as SummarySectionValue);
    }

    return title;
};
