import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewsAISummaryFeedbackModal } from '../vendors-security-reviews-ai-summary-feedback-modal';

const AI_SUMMARY_FEEDBACK_MODAL_ID = 'ai-summary-feedback-modal';

export const openAISummaryFeedbackModal = action((): void => {
    modalController.openModal({
        id: AI_SUMMARY_FEEDBACK_MODAL_ID,
        content: () => (
            <VendorsSecurityReviewsAISummaryFeedbackModal data-id="ai-summary-feedback-modal" />
        ),
        centered: true,
        disableClickOutsideToClose: false,
        size: 'md',
    });
});

export const closeAISummaryFeedbackModal = action((): void => {
    modalController.closeModal(AI_SUMMARY_FEEDBACK_MODAL_ID);
});
