import { sharedVendorsProfileQuestionnaireAISummaryController } from '@controllers/vendors';
import { Accordion } from '@cosmos/components/accordion';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const VendorSecurityReviewsAISummaryErrorComponent = observer(
    (): React.JSX.Element => {
        const { summaryError, summaryErrorTitle, canRetryError, retrySummary } =
            sharedVendorsProfileQuestionnaireAISummaryController;

        return (
            <>
                {summaryError && summaryErrorTitle && (
                    <Accordion
                        title={summaryErrorTitle}
                        data-id="security-review-file-ai-summary-error"
                        data-testid="VendorSecurityReviewsAISummaryComponent-Error"
                        supportingContent={
                            <Metadata label={t`Beta`} type="tag" />
                        }
                        iconSlot={{
                            slotType: 'icon',
                            typeProps: {
                                name: 'AI',
                                colorScheme: 'neutral',
                            },
                        }}
                        body={
                            <Stack direction="column" gap="4x">
                                <Text
                                    type="body"
                                    size="200"
                                    colorScheme="neutral"
                                >
                                    {summaryError}
                                </Text>
                                {canRetryError && (
                                    <Button
                                        label={t`Generate summary`}
                                        level="secondary"
                                        data-id="retry-summary-button"
                                        onClick={retrySummary}
                                    />
                                )}
                            </Stack>
                        }
                    />
                )}
            </>
        );
    },
);
