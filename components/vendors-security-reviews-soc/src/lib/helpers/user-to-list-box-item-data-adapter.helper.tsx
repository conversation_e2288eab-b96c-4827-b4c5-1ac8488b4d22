import { Avatar } from '@cosmos/components/avatar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { getFullName, getInitials } from '@helpers/formatters';

/**
 * Helper function to convert UserResponseDto to ListBoxItemData format.
 * Used for formatting user data in reviewer selections.
 */
export const userToListBoxItemDataAdapter = (
    user: UserResponseDto,
): ListBoxItemData => {
    const fullName = getFullName(user.firstName, user.lastName);

    return {
        id: user.id.toString(),
        label: fullName,
        startSlot: (
            <Avatar
                fallbackText={getInitials(fullName)}
                imgSrc={user.avatarUrl ?? undefined}
                imgAlt={fullName}
                size="xs"
            />
        ),
        value: user.id.toString(),
    };
};
