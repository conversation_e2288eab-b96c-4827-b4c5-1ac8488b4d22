import { t } from '@globals/i18n/macro';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export const openSocClearFormConfirmation = ({
    onConfirm,
}: {
    onConfirm: () => void;
}): void => {
    openConfirmationModal({
        title: t`Clear form`,
        body: t`Confirm that you'd like to clear this Report Review.`,
        confirmText: t`Yes, clear form`,
        cancelText: t`No, take me back`,
        type: 'danger',
        onConfirm: () => {
            onConfirm();
            closeConfirmationModal();
        },
        onCancel: () => {
            closeConfirmationModal();
        },
    });
};
