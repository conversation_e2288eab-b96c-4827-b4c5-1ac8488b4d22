import { sharedAddTestToProgramController } from '@controllers/add-test-to-program';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { LibraryTestTemplateError } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type { LibraryTestTemplateBulkAddErrorTableNameCellProps } from '../../types/library-test-template-bulk-add-error-table.types';

const errorTypeComponent = (errorType: LibraryTestTemplateError) => {
    if (errorType === 'MISSING_CONNECTION') {
        const title = sharedAddTestToProgramController.isMultiProduct
            ? t`Missing connection in at least one workspace`
            : t`Missing connection`;

        return (
            <Feedback
                severity="primary"
                title={title}
                data-id="missing-connection-feedback"
            />
        );
    }

    return (
        <Feedback
            severity="warning"
            title={t`Not supported`}
            data-id="not-supported-feedback"
            data-testid="errorTypeComponent"
        />
    );
};

export const BulkAddTestNameCell = ({
    row: { original },
}: LibraryTestTemplateBulkAddErrorTableNameCellProps): React.JSX.Element => {
    const {
        nameCell: { name, errorType },
    } = original;

    return (
        <Stack
            direction="column"
            gap="sm"
            data-id="bulk-add-test-name-cell"
            data-testid="BulkAddTestNameCell"
        >
            <Text type="body" colorScheme="neutral">
                {name}
            </Text>
            {errorTypeComponent(errorType)}
        </Stack>
    );
};
