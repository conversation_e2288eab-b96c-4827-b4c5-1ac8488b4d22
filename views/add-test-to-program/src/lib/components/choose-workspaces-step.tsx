import { size } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import {
    sharedAddTestToProgramController,
    type Workspace,
} from '@controllers/add-test-to-program';
import { modalController } from '@controllers/modal';
import { Avatar } from '@cosmos/components/avatar';
import { Banner } from '@cosmos/components/banner';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { Identity } from '@cosmos-lab/components/identity';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer, toJS } from '@globals/mobx';
import { openAddWorkspacesModal } from '../helpers/add-workspaces-open-modal';

export const ChooseWorkspacesStep = observer((): React.JSX.Element => {
    const {
        selectedWorkspaces,
        setSelectedWorkspaces,
        isWorkspacesStepValid,
        resetWorkspacesValidation,
        availableWorkspaces,
    } = sharedAddTestToProgramController;

    const workspacesData = toJS(selectedWorkspaces);

    const handleConfirmWorkspaces = (workspaces: Workspace[]) => {
        setSelectedWorkspaces(workspaces);
        resetWorkspacesValidation();
    };

    const handleRemoveWorkspace = action((workspaceId: string) => {
        sharedAddTestToProgramController.removeWorkspace(workspaceId);
    });

    const handleRemoveAll = () => {
        setSelectedWorkspaces([]);
        resetWorkspacesValidation();
    };

    const handleAddWorkspace = action(() => {
        openAddWorkspacesModal({
            availableWorkspaces,
            initialSelectedWorkspaces: workspacesData,
            onConfirm: handleConfirmWorkspaces,
            onClose: () => {
                modalController.closeModal('add-workspaces-modal');
            },
        });
    });

    return (
        <Stack
            direction="column"
            gap="4x"
            data-testid="ChooseWorkspacesStep"
            data-id="dIG0PThl"
        >
            <Text type="subheadline" size="400">
                <Trans>Choose Workspaces</Trans>
            </Text>

            {!isWorkspacesStepValid && (
                <Banner
                    displayMode="section"
                    severity="critical"
                    title=""
                    body={t`At least one workspace must be selected to continue`}
                    data-testid="workspaces-validation-banner"
                    data-id="workspaces-validation-banner"
                />
            )}

            <Stack direction="row" gap="2x" align="center" justify="between">
                <Stack direction="row" gap="2x" align="center">
                    <Text type="title" size="200">
                        <Trans>Selected workspaces</Trans>
                    </Text>
                    <Metadata
                        type="number"
                        colorScheme="neutral"
                        label={selectedWorkspaces.length.toString()}
                        data-testid="selected-workspaces-count"
                        data-id="workspace-count-pill"
                    />
                </Stack>

                {size(selectedWorkspaces) > 0 && (
                    <Stack direction="row" gap="2x" align="center">
                        <Button
                            label={t`Remove all`}
                            level="tertiary"
                            colorScheme="danger"
                            data-testid="remove-all-workspaces-button"
                            data-id="remove-all-workspaces-button"
                            onClick={handleRemoveAll}
                        />
                        <Button
                            label={t`Add workspace`}
                            level="secondary"
                            colorScheme="primary"
                            data-testid="add-workspace-button-header"
                            data-id="add-workspace-button-header"
                            onClick={handleAddWorkspace}
                        />
                    </Stack>
                )}
            </Stack>

            {size(workspacesData) === 0 ? (
                <>
                    <Divider />
                    <Box px="6x">
                        <Stack height="100%">
                            <EmptyState
                                isInline
                                title=""
                                description={t`Start your configuration by choosing the workspaces to import this template`}
                                rightAction={
                                    <Button
                                        label={t`Add workspace`}
                                        level="secondary"
                                        colorScheme="primary"
                                        data-testid="add-workspace-button"
                                        data-id="add-workspace-button"
                                        onClick={handleAddWorkspace}
                                    />
                                }
                            />
                        </Stack>
                    </Box>
                </>
            ) : (
                <AppDatatable
                    hidePagination
                    isLoading={false}
                    isSortable={false}
                    tableId="selected-workspaces-table"
                    data-id="selected-workspaces-table"
                    data={workspacesData}
                    total={workspacesData.length}
                    columns={[
                        {
                            id: 'name',
                            accessorKey: 'label',
                            header: () => t`Workspaces`,
                            cell: ({ row }) => (
                                <Identity
                                    data-testid="workspace-name-cell"
                                    data-id={`workspace-name-cell-${row.original.id}`}
                                    primaryLabel={row.original.label}
                                    size="sm"
                                    figure={
                                        <Avatar
                                            data-id="cosmos-identity-avatar"
                                            size="xs"
                                            fallbackText={row.original.label.slice(
                                                0,
                                                2,
                                            )}
                                        />
                                    }
                                />
                            ),
                        },
                        {
                            id: 'trash',
                            accessorKey: 'id',
                            header: '',
                            cell: ({ row }) => (
                                <Stack
                                    justify="end"
                                    data-id="trash-icon-container"
                                >
                                    <Button
                                        isIconOnly
                                        label={t`Remove workspace`}
                                        startIconName="Trash"
                                        level="tertiary"
                                        colorScheme="danger"
                                        data-testid="remove-workspace-button"
                                        data-id="remove-workspace-button"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleRemoveWorkspace(
                                                row.original.id,
                                            );
                                        }}
                                    />
                                </Stack>
                            ),
                            meta: {
                                shouldIgnoreRowClick: true,
                            },
                        },
                    ]}
                    tableSearchProps={{
                        hideSearch: true,
                    }}
                />
            )}
        </Stack>
    );
});
