import { isNil } from 'lodash-es';
import { useEffect } from 'react';
import { sharedVendorsSecurityReviewFileController } from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';

export const VendorsProfileSecurityReviewFilesView = observer(
    ({
        vendorType,
    }: {
        vendorType: 'current' | 'prospective';
    }): React.JSX.Element => {
        const { isLoading, pdfDownloadUrl } =
            sharedVendorsSecurityReviewFileController;

        const { isVendorsDomainReadEnabled } = sharedFeatureAccessModel;
        const { currentWorkspace } = sharedWorkspacesController;
        const navigate = useNavigate();

        useEffect(() => {
            if (!isVendorsDomainReadEnabled && currentWorkspace?.id) {
                navigate(
                    `/workspaces/${currentWorkspace.id}/vendors/${vendorType}`,
                );
            }
        }, [
            isVendorsDomainReadEnabled,
            currentWorkspace?.id,
            vendorType,
            navigate,
        ]);

        if (!isVendorsDomainReadEnabled) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <>
                {isLoading || isNil(pdfDownloadUrl) ? (
                    <Loader label={t`Loading...`} />
                ) : (
                    <PdfViewer
                        src={pdfDownloadUrl.signedUrl}
                        label={t`pdf-viewer`}
                    />
                )}
            </>
        );
    },
);
