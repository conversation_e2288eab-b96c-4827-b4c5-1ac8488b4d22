import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ChoiceCardStories from './ChoiceCard.stories';

<Meta of={ChoiceCardStories} />

<Title />

<Description />

<Primary />

<Controls of={ChoiceCardStories.Playground} />

## Import

```jsx
import { ChoiceCard } from '@cosmos-lab/components/choice-card';
```

## 🟢 When to use the component

- **Options need explanation** - When choices require additional context, descriptions, or visual elements beyond a simple label
- **Prominent decision points** - When the selection is a primary focus of the interface and deserves visual emphasis
- **Complex option comparison** - When users benefit from seeing detailed information about each choice side-by-side
- **Visual content enhances understanding** - When icons, images, or custom content help clarify the options

## ❌ When not to use the component

- **Simple, self-explanatory choices** - Use standard form controls when labels alone are sufficient
- **Space efficiency is critical** - ChoiceCards require more vertical space than compact form controls
- **Quick, routine selections** - Standard inputs are faster for frequent or secondary decisions
- **Long option lists** - Consider [SelectField](https://cosmos.drata.com/?path=/docs/forms-selectfield--docs) or [ComboboxField](https://cosmos.drata.com/?path=/docs/forms-comboboxfield--docs) when presenting many choices

## 🛠️ How it works

#### Usage guidelines

- Use ChoiceCard when you need to present options that require more context than standard checkboxes or radio buttons
- Ideal for selections that benefit from visual content like icons, images, or additional descriptive text
- Best suited for scenarios where the selection options are the primary focus of the interface
- Combine multiple ChoiceCards using ChoiceCardGroup for related selections

#### Using input types

**Checkbox type**
- **Definition:** Allows multiple selections from a group of options
- **Purpose:** When users can select zero, one, or multiple items

**Radio type**
- **Definition:** Allows only one selection from a group of mutually exclusive options
- **Purpose:** When users must choose exactly one option from the available choices

### Usability

**Selection behavior:**
- Multiple cards can be selected simultaneously (checkbox variant)
- Only one card can be selected at a time (radio variant)
- Clicking anywhere on the card toggles/selects the option
- Visual feedback shows selected state immediately

**Interaction states:**
- **Default** - Ready for interaction
- **Hover** - Visual preview of selection
- **Focus** - Keyboard navigation indicator
- **Selected** - Persistent selection state
- **Disabled** - Non-interactive state

**Keyboard navigation:**
- **Tab** - Navigate between ChoiceCards in logical order
- **Space** - Toggle checkbox selection or select radio option
- **Arrow keys** - Navigate between radio options in a group
- **Enter** - Alternative activation method for selection

### Content

**Labels:**
- Use sentence case (capitalize only the first word and proper nouns)
- Keep labels short and descriptive
- Make labels scannable and distinct from each other

**Content patterns:**
- Use parallel structure across related ChoiceCards
- Include relevant details in help text rather than cramming into labels
- Maintain consistent tone and terminology
- Avoid articles (a, an, the) in labels when possible

**Wrapping and truncation:**
- Labels should be concise to prevent wrapping
- Help text can wrap to multiple lines if necessary
- Consider visual weight and balance for slot content

### Accessibility

**What the design system provides:**
- Proper ARIA labeling and descriptions automatically applied
- Keyboard navigation support built into the component
- Focus management and visual focus indicators
- Screen reader announcements for state changes
- Semantic HTML structure with proper form associations
- High contrast mode compatibility
- Touch target sizing meets accessibility guidelines (minimum 44px)

**Development responsibilities:**
- Ensure ChoiceCards are properly associated with form labels and validation
- Implement accessible error messaging for required selections
- Use ChoiceCardGroup with proper fieldset and legend elements
- Announce content changes to screen readers when cards update
- Provide clear, accessible feedback for validation errors

**Design responsibilities:**
- Ensure sufficient contrast ratios for all text and interactive elements
- Design clear, visible focus states that meet WCAG guidelines
- Structure content to work well with screen readers
- Design accessible error messaging that's clearly associated with the component
- Don't rely solely on color to convey selection state