import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as NavigationMenuCosmosLabStories from './NavigationMenuCosmosLab.stories';

<Meta of={NavigationMenuCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={NavigationMenuCosmosLabStories.Playground} />

## Import

```jsx
import { NavigationMenuCosmosLab } from '@cosmos-lab/components/navigation-menu';
```

## 🟢 When to use the component

- **Primary site navigation** - For main navigation menus in headers or top-level layouts with multiple sections
- **Complex navigation structures** - For navigation that has multiple levels or hierarchical organization
- **Keyboard-accessible menus** - When users need to navigate menus using keyboard controls and screen readers

## ❌ When not to use the component

- **Simple link lists** - Use basic links or lists for straightforward navigation without dropdowns
- **Breadcrumb trails** - Use Breadcrumbs component to show the current page's location in the site hierarchy
- **Content tabs** - Use Tabs component for switching between different views of the same content
- **Mobile-only menus** - Consider simpler mobile patterns like slide-out drawers for mobile-specific navigation
- **Single-level navigation** - For navigation with just a few top-level links, simpler components may be more appropriate

## 🛠️ How it works

The NavigationMenu component provides a compound component system built on Radix UI's NavigationMenu primitive, offering accessible navigation with dropdown support and keyboard interaction.

**Component architecture:**
- **NavigationMenuRoot** - Root container that provides context and manages navigation state
- **NavigationMenuList** - List container that renders as `<ul>` and manages focus and keyboard navigation
- **NavigationMenuItem** - Individual menu item that renders as `<li>` and can contain links or triggers
- **NavigationMenuLink** - Link component for navigation items, supports `href` prop and `asChild` pattern
- **NavigationMenuTrigger** - Button component that opens dropdown content when clicked or focused
- **NavigationMenuContent** - Container for dropdown/submenu content that appears when trigger is activated

**Interaction patterns:**
- **Link navigation** - Direct navigation using `NavigationMenuLink` with `href` prop
- **Dropdown menus** - Expandable content using `NavigationMenuTrigger` and `NavigationMenuContent` pairs
- **Keyboard support** - Built-in arrow key navigation, Enter/Space activation, and Escape to close
- **Focus management** - Automatic focus handling for dropdown opening/closing and keyboard navigation

**Technical features:**
- **Radix UI foundation** - Built on `@radix-ui/react-navigation-menu` for robust accessibility and behavior
- **Compound components** - Each component handles specific functionality while working together as a system
- **Data attributes** - All components include `data-testid` and `data-id` attributes for testing and identification
- **Flexible rendering** - Supports `asChild` pattern for custom rendering while maintaining accessibility

### Usability

**Navigation structure:**
- **Logical hierarchy** - Organize menu items in a logical structure that matches your application's information architecture
- **Consistent patterns** - Use similar interaction patterns across all navigation menus in your application
- **Appropriate depth** - Limit dropdown nesting to maintain usability and avoid overly complex menu structures

**Content organization:**
- **Clear labeling** - Use descriptive labels for both links and trigger buttons that clearly indicate their purpose
- **Grouped content** - Organize related navigation items together within dropdown content areas
- **Predictable behavior** - Ensure navigation behavior is consistent and matches user expectations

### Content

**Navigation labels:**
- **Descriptive text** - Use clear, concise labels that accurately describe the destination or content
- **Consistent terminology** - Maintain consistent language and naming conventions across navigation items
- **Scannable structure** - Organize content so users can quickly scan and find what they're looking for

**Menu structure:**
- **Logical grouping** - Group related navigation items together in dropdown content
- **Appropriate triggers** - Use trigger buttons for sections that contain multiple related links
- **Direct links** - Use direct links for single destinations that don't require dropdown content

### Accessibility

**What the design system provides:**
- Full keyboard navigation support with arrow keys, Enter/Space activation, and Escape to close menus
- Proper ARIA attributes and roles for screen reader compatibility and navigation announcement
- Focus management that handles dropdown opening/closing and maintains logical focus order
- Semantic HTML structure with proper list elements and navigation landmarks
- High contrast support and scalable design that works with accessibility preferences

**Development responsibilities:**
- Navigation structure to ensure logical menu hierarchy that works well with screen readers
- Link destinations to ensure all navigation links lead to accessible, meaningful pages
- Content organization to group related items logically for keyboard and screen reader navigation
- Testing with assistive technology to verify navigation works properly across different accessibility tools

**Design responsibilities:**
- Visual hierarchy that clearly shows navigation structure and current state
- Focus indicators that provide clear visual feedback for keyboard navigation
- Sufficient contrast for all navigation elements against background colors
- Responsive behavior that maintains accessibility across different screen sizes and input methods
