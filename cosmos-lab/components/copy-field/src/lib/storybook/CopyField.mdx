import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as CopyFieldStories from './CopyField.stories';

<Meta of={CopyFieldStories} />

<Title />

<Description />

<Primary />

<Controls of={CopyFieldStories.Playground} />

## Import

```jsx
import { CopyField } from '@cosmos-lab/components/copy-field';
```

## 🟢 When to use the component

- **Sharing values** - When users need to copy URLs, tokens, API keys, or configuration strings for use elsewhere
- **Read-only data display** - For displaying important values that users frequently need to reference or share
- **One-click copying** - When the primary action users want to take with displayed text is copying it

## ❌ When not to use the component

- **Editable content** - Use TextField or TextareaField when users need to modify the content
- **Simple text display** - Use Text component when copying functionality isn't needed
- **Sensitive information** - Consider security implications before allowing easy copying of sensitive data

## 🛠️ How it works

The CopyField component displays read-only text with an integrated copy-to-clipboard button, providing users with easy access to copy important values like URLs, tokens, or configuration strings.

**Component structure:**
- **TextField integration** - Uses TextField component in read-only mode for consistent form field styling
- **Copy button** - Integrated Button component with copy icon and feedback states
- **Clipboard API** - Uses modern `navigator.clipboard.writeText()` for secure copying
- **Visual feedback** - Button text and icon change to "Copied!" state with automatic reset

**Copy functionality:**
- **One-click copying** - Single button click copies the entire field value to clipboard
- **Error handling** - Graceful fallback with console error logging if clipboard access fails
- **Feedback timing** - "Copied!" state displays for 1 second before reverting to default
- **Accessibility** - Button includes descriptive `a11yLabelOverride` for screen readers

**Form integration:**
- **Form field structure** - Accepts `formId` and `name` props for proper form association
- **Read-only state** - TextField is always read-only with `onChange={noop}` to prevent editing
- **Label support** - Supports standard form field labeling for accessibility and context
- **Consistent styling** - Maintains design system consistency with other form components

### Usability

**Copy experience:**
- **Clear purpose** - Button clearly indicates copy functionality with icon and label
- **Immediate feedback** - Visual confirmation when copy action succeeds
- **Error resilience** - Handles clipboard API failures gracefully without breaking user experience
- **Keyboard accessible** - Copy button is fully keyboard navigable and activatable

**Content presentation:**
- **Full visibility** - Entire value is visible in the text field for user verification
- **Read-only clarity** - Users understand the content cannot be edited, only copied
- **Consistent layout** - Aligns with other form fields for cohesive form design
- **Responsive behavior** - Button and field adapt appropriately to different screen sizes

**Interaction patterns:**
- **Single action** - One click/keypress copies the complete value
- **Visual confirmation** - Button state change provides clear success feedback
- **Quick reset** - Automatic return to default state allows for repeated copying
- **Touch-friendly** - Button sizing appropriate for touch interaction on mobile devices

### Content

**Field labeling:**
- **Descriptive labels** - Use clear labels that explain what value is being displayed (e.g., "API Key", "Webhook URL")
- **Context clarity** - Labels should help users understand the purpose and use of the copyable value
- **Consistent terminology** - Use consistent labeling patterns across similar copy fields in the application
- **Action context** - Labels should make it clear that the value is meant to be copied and used elsewhere

**Value presentation:**
- **Complete values** - Display the full value that will be copied, avoiding truncation when possible
- **Readable formatting** - Present values in a format that's easy to scan and verify before copying
- **Meaningful content** - Ensure the displayed value is exactly what users need in their target destination
- **Consistent formatting** - Use consistent value formatting across similar copy fields

**Copy button labeling:**
- **Clear action** - "Copy" label clearly indicates the button's function
- **Success feedback** - "Copied!" provides immediate confirmation of successful copy action
- **Accessible description** - Screen reader text explains both the action and the context of what's being copied
- **Consistent language** - Use consistent copy-related terminology across the application

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships and button labeling
- Full keyboard navigation support including Tab navigation and Space/Enter activation for the copy button
- Screen reader announcements for copy actions and success states through descriptive button labels
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators for both the text field and copy button
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile interaction

**Development responsibilities:**
- Provide descriptive labels that give clear context for what value is being displayed and copied
- Ensure the `label` prop creates meaningful context for screen reader users
- Use appropriate `formId` and `name` props for proper form association when used within forms
- Handle copy failures gracefully without breaking the user experience or accessibility
- Ensure the copied value matches exactly what users expect based on the displayed content
- Consider the security implications of the values being made easily copyable

**Design responsibilities:**
- Provide sufficient color contrast for all field states, button states, and text content across different themes
- Design clear visual hierarchy that shows the relationship between the label, value, and copy action
- Ensure focus indicators are clearly visible and meet contrast requirements for both the field and button
- Create consistent visual patterns for copy fields across the application
- Design appropriate spacing and sizing for the copy button that works well with the text field layout
- Ensure the "Copied!" success state is visually distinct and provides clear feedback without being disruptive

