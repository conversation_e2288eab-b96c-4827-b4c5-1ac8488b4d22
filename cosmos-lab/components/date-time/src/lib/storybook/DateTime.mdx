import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as DateTimeStories from './DateTime.stories';

<Meta of={DateTimeStories} />

<Title />

<Description />

<Primary />

<Controls />

## Import

```jsx
import { DateTime } from '@cosmos-lab/components/date-time';

// Basic usage with a specific date (date is required)
<DateTime date="2024-10-15" />

// With specific format
<DateTime date={new Date()} format="sentence_time" />

// With date range format (requires endDate)
<DateTime
  date={new Date()}
  format="sentence_range"
  endDate={new Date(new Date().setDate(new Date().getDate() + 14))}
/>

// Disable tooltip for formats that normally show one
<DateTime date={new Date()} format="table" isTooltipDisabled />

// Customize the Text component that wraps the formatted date
<DateTime
  date={new Date()}
  textProps={{
    type: 'headline',
    size: '300',
    colorScheme: 'primary',
    align: 'center'
  }}
/>
```

## Available Formats

The DateTime component supports the following formats:

- `sentence`: 'October 5, 2024'
- `sentence_time`: 'October 15, 2024 at 12:04pm'
- `field`: 'Oct 5, 2024'
- `field_time`: 'Oct 5, 2023 at 5:03pm'
- `table`: 'Oct 05, 2024'
- `table_time`: 'Oct 05, 2024 at 5:03pm'
- `sentence_range`: 'October 5, 2024 to October 15, 2024'
- `field_range`: 'Oct 5, 2024 to Oct 15, 2024'
- `table_range`: 'Oct 05, 2024 to Oct 15, 2024'
- `time`: '12:04pm'
- `overdue`: '3 days ago' or '4 months ago' (uses days for periods up to 90 days, then months for longer periods)

### Notes

* The `datetime_attr` and `date_tooltip` formats available in `@helpers/date-time` are reserved for internal use and are therefore excluded from the available formats list.
* The `timestamp` format is intended for CSV exports and JSON evidence.

## Tooltips

The following formats automatically display a tooltip on hover showing the full date and time in the `date_tooltip` format:

- `table`
- `table_time`
- `table_range`
- `overdue`
- `sentence`
- `sentence_time`
- `time`
- `sentence_range`

You can disable this behavior by setting the `isTooltipDisabled` prop to `true`.

## With Text props

<Canvas of={DateTimeStories.WithTextProps} />

This example shows how to use the `textProps` prop to customize the appearance of the text within the DateTime component.

## 🟢 When to use the component

- **Consistent date formatting** - When you need standardized date and time display across the application using design system formats
- **Contextual date display** - For showing dates in tables, forms, sentences, or other UI contexts with appropriate formatting
- **Tooltip enhancement** - When you want automatic tooltips showing complete date/time information for abbreviated formats
- **Accessibility compliance** - For screen reader compatible date display with proper semantic markup
- **Internationalization support** - When displaying dates that need to work across different locales and user preferences
- **Range display** - For showing date ranges with consistent formatting and proper separators

## ❌ When not to use the component

- **Interactive date elements** - Use DatePickerField or other form components for date input and selection
- **Custom formatting needs** - When you need date formats not supported by the component's available options
- **Real-time updates** - For timestamps that need frequent updates, consider specialized components

## 🛠️ How it works

The DateTime component formats and displays dates consistently using the `@helpers/date-time` formatting system with built-in accessibility features and tooltip support. Dates should be optimized for ease of understanding based on the context. Users should be able to select between US standard (December 3, 2022) and international (03 3 December, 2022) and we should account for both.

### Format Selection Guidelines

#### Dates

**Complete ideas or full sentences**

- No abbreviations (write out the month)
- Exclude leading zeroes
- Year can depend on context
    - (for example, if dates are already grouped by year, it isn’t needed)
- Use a comma

✅ Correct: “Next task due on June 8, 2019”

❌ Incorrect: “Next task due on Jun 08, 2019”

**Fields, cells, or after labels**

- Use 3-letter month abbreviations
    - Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec
        1. Capital first letter, no period
        2. Abbreviations can be used across the board unless written in a sentence.
- Include leading zeroes if in a table
    - (this helps line up month, date, and year to make more scannable)
- Always include year

✅ Correct for table: “Jan 02, 2011”

❌ Incorrect: “Dec 2, 2023”

**Date ranges**

For date ranges, include leading zeroes and separate by an en dash with no spaces.

- Write out the range in complete ideas or full sentences. (this should be rare)
    - ✅ Correct: “I was king from April 22, 1509 to January 28, 1547.”
    - ❌ Incorrect: “I was king from 04/22/1509–01/28/1547.”

In tables, fields, or after labels

- This is the one place to use numerical dates
    - ✅ Correct: “Audit period: Jun 22, 1509 to Jan 28, 1547
    - ❌ Incorrect:  “Audit period: 04/22/1509–01/28/1547”

#### Times

Users should be able to select between US standard (am/pm) and international (24 hour clock) and we should account for both.

For US, AM and PM should be stylized with no space and no period.

✅ Correct: 12:04pm

**Future times**

Future times are generally used to set an expectation so specifics matter. We should include the specific time, date, and timezone in order to better support clear expectations between enterprise users working in different timezones. These should be shown based on the individual users settings. In some cases, it might make sense to simplify the date or time of day, but the user should at least be able to hover the date to be sure of the specific time, including seconds and timezone.

1. Format example (US): Next task due by Jun 11 at 5:52pm
    1. Add a hover (section below Past times)
2. Format example (world → London): Next task due by 11 Jun at 17:52
    1. Add a hover (section below Past times)

**Past times**

Most past objects should use a simple date format if the intent is for record keeping. However, if the subject has an expectation attached to it (like an incomplete task), past tense should use a “[time] ago” format to make it easier to understand the relative time.

Both can benefit from hover text with specific time, including seconds and timezone.

Expectation example (Tasks, tickets)

✅ Correct: “Due 2 hours ago”, “Due 2 days ago”, “Due 2 weeks ago”, “Due 2 years ago”

- Add a hover (section below Past times)

❌ Incorrect: “Dec 2, 2023”

❌ Incorrect: “Due in 3 days” (only use relative dates for past times)

Event example (Last updated, past actions performed)

✅ Correct: “Dec 02, 2023 at 5:03pm”

- Add a hover (section below Past times)

❌ Incorrect: “Happened 4 weeks ago”

❌ Incorrect: “Dec 02, 2023 @ 5:03pm”

**Referring to a month**

When a group of information is organized by month, always include the year. Examples include tasks and vendor security reviews.

✅ Correct: “April 2025”

❌ Incorrect: “April”

**Extended times on hover (tooltips)**

In many cases, having a complete date available to a user is wise for precision. When applicable, include a tooltip containing the complete date and time value.

1. Hover would show the same with EST appended.
    1. Jun 11, 2023 at 5:52:03pm EST
2. Hover would show the same with GMT+1 appended.
    1. Jun 11, 2023 at 17:52:03pm GMT+1

✅ Correct: “October 15, 2024” in a sentence when the time is also available in the underlying data.

✅ Correct: “Oct 15, 2024 at 12:04pm” in a table cell

❌ Incorrect: “3 days ago” without a tooltip

For accessibility reasons, tooltips cannot be used when a date is used in an interactive element, such as `input` or `button`. When us

**Timestamps**

Timestamps should be used for exports, JSONs, and Auditor materials.

Default for timestamps should use the ISO 8601 format in the UTC timezo

- 2024-10-16T01:00:00Z

#### Date and time settings

These should be personal settings along country and language.

1. Timezone
    1. Users should be able to detect their timezone
    2. We should be able to detect change in timezone from OS and suggest updating.
2. Time format
    1. Format example (US): 5:52pm
    2. Format example (world): 17:52
3. Date format
    1. Format example (US): Oct 5, 2023
    2. Format example (world): 5 Oct, 2023

#### Symbols

We don’t use the @ symbol for times or timestamps. Write out “at” when necessary.

#### Exceptions

If a time comes from an external source, such as JIRA, then consider if the time formatting should be consistent (unless it would create local clarity issues in Drata).

### Usability

**Component integration:**
- **Text customization** - Use `textProps` to match surrounding typography and visual hierarchy
- **Responsive behavior** - Component adapts to container constraints while maintaining readability
- **Performance** - Memoized formatting reduces unnecessary re-renders in data-heavy interfaces

### Content

**Date handling requirements:**
- **Required date prop** - Component always requires a valid date value (Date object or string)
- **Range support** - Range formats (`sentence_range`, `field_range`, `table_range`) require both `date` and `endDate` props
- **Timezone awareness** - Displays dates in user's local timezone with detailed tooltip information
- **Invalid date handling** - Component gracefully handles invalid dates with fallback display

**Tooltip behavior:**
- **Automatic enhancement** - Most formats include detailed tooltips with complete date/time information
- **Timezone information** - Tooltips include timezone details for user precision
- **Disable option** - Use `isTooltipDisabled` when tooltips interfere with layout or accessibility requirements

### Accessibility

**What the design system provides:**
- Semantic `<time>` element with proper `dateTime` attribute for screen readers and assistive technology
- Automatic tooltip functionality that works with keyboard navigation and screen readers
- High contrast support that works with system preferences and maintains readability
- Scalable text that adapts to user zoom preferences and accessibility settings
- Proper ARIA attributes and semantic structure for date/time information

**Development responsibilities:**
- Choose appropriate formats that provide sufficient context for screen reader users
- Ensure tooltip content is meaningful and provides additional value beyond the displayed text
- Test with screen readers to verify date information is announced clearly
- Consider that tooltips cannot be used in interactive elements (buttons, inputs) for accessibility compliance
- Verify that relative dates ("2 days ago") have sufficient context for understanding

**Design responsibilities:**
- Design clear visual hierarchy that helps users understand date context and importance
- Ensure sufficient contrast ratios for date text in all supported color schemes
- Consider user preferences for date/time formatting and timezone display
- Design consistent patterns for similar date contexts across the application
- Maintain readability when dates are displayed in constrained spaces or data-dense interfaces

