import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as WordmarkCosmosLabStories from './WordmarkCosmosLab.stories';

<Meta of={WordmarkCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={WordmarkCosmosLabStories.Playground} />

## Import

```jsx
import { WordmarkCosmosLab } from '@cosmos-lab/components/wordmark-cosmos-lab';
```

## 🟢 When to use the component

- **Brand identification** - When you need to display the Drata logo for brand recognition and identity
- **Application headers** - For consistent brand presence in navigation bars and page headers
- **Authentication pages** - On login, signup, and other authentication flows where brand trust is important
- **Customer vs auditor contexts** - When you need to distinguish between customer and auditor experiences with appropriate branding

## ❌ When not to use the component

- **Interactive elements** - Don't use as a button or clickable element without proper interactive component wrapping
- **Decorative purposes** - Don't use the logo as decoration or filler content
- **Size constraints** - Don't use when the logo would be too small to maintain brand integrity and readability

## 🛠️ How it works

The Wordmark component displays the Drata brand logo with customizable sizing and color schemes for consistent brand representation across the application.

**Logo variants:**
- **Customer type** (`type="customer"`) - Displays the Drata Words logo for customer-facing applications (default)
- **Auditor type** (`type="auditor"`) - Shows the Drata Audit Hub logo for auditor-specific interfaces
- **SVG-based rendering** - Uses scalable vector graphics (`DrataWordsLogo` and `DrataAuditHubLogo`) for crisp display

**Styling system:**
- **Height control** - Uses `Dimension` type for height prop with 'md' as default
- **Color schemes** - Supports 'neutral' and 'inverted' color schemes using `COLOR_SCHEMES` from icon components
- **Styled components** - Uses `StyledWordmarkDiv` with `currentColor` for SVG color inheritance

**Technical features:**
- **Semantic markup** - Renders with `role="img"` and `aria-label="Drata logo"` for accessibility
- **Test integration** - Includes `data-testid="Wordmark"` and `data-id="wordmark"` attributes
- **Design token integration** - Uses `useDimensionProps` hook and color scheme constants

### Usability

**Logo selection:**
- **Customer context** - Use default `type="customer"` for standard customer-facing applications
- **Auditor context** - Use `type="auditor"` for auditor-specific interfaces and workflows
- **Consistent usage** - Use the same logo type consistently throughout a single user experience

**Sizing and placement:**
- **Height sizing** - Choose appropriate dimension values that maintain logo legibility
- **Color contrast** - Select 'neutral' or 'inverted' color schemes based on background context
- **Spacing considerations** - Ensure adequate white space around the logo for proper visual breathing room

### Content

**Brand guidelines:**
- **Logo integrity** - The component renders official Drata logos without modification
- **Context-appropriate variants** - Use customer vs auditor logos based on the target user experience
- **Consistent placement** - Follow standard positioning patterns for brand recognition

**Implementation patterns:**
- **Header branding** - Standard placement in application headers with appropriate height sizing
- **Authentication flows** - Centered placement on login and signup pages to establish trust
- **Interactive wrapping** - Wrap with Link or Button components when logo should be clickable

### Accessibility

**What the design system provides:**
- Semantic image markup with `role="img"` attribute for screen reader recognition
- Descriptive `aria-label="Drata logo"` for assistive technology
- High contrast support through 'neutral' and 'inverted' color scheme options
- Scalable vector graphics that maintain clarity at different zoom levels

**Development responsibilities:**
- Context awareness to choose appropriate logo type (customer vs auditor) based on user experience
- Interactive wrapping when the logo should be clickable using proper Link or Button components
- Color scheme selection to ensure sufficient contrast against background colors

**Design responsibilities:**
- Appropriate sizing using dimension props that maintains brand integrity
- Sufficient contrast between logo colors and background elements
- Consistent placement patterns that align with brand guidelines and user expectations

