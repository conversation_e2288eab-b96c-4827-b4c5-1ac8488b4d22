import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as DividerStories from './Divider.stories';

<Meta of={DividerStories} />

<Title />

<Description />

<Primary />

<Controls of={DividerStories.Playground} />

## Import

```jsx
import { Divider } from '@cosmos-lab/components/divider';
```

## 🟢 When to use the component

- **App header sections** - Separate logo areas from workspace pickers or navigation
- **Content navigation boundaries** - Create clear separation between navigation menus and main content
- **Vertical separations** - Use in horizontal layouts to separate adjacent elements
- **Panel content sections** - Divide different areas within cards, panels, or modals
- **Content boundaries** - Create visual breaks between related but distinct content areas

## ❌ When not to use the component

- **Within tight layouts** - When spacing and typography already provide clear separation
- **Between unrelated content** - Use layout containers instead of dividers for major content divisions
- **In simple interfaces** - When content structure is already clear without visual aids
- **Layout components** - Use Stack, Grid, or Box components for structural separation
- **Typography hierarchy** - Let headings and text styles create natural content flow

## 🛠️ How it works

The Divider component provides visual separation between content sections with support for different orientations and sizes.

**Size options:**
- **Small (`sm`)** - 1px thickness, default size for most content separation needs
- **Medium (`md`)** - 2px thickness, used for stronger visual emphasis when needed

**Orientation variants:**
- **Horizontal (default)** - Used for separating stacked content sections, form groups, panel areas
- **Vertical** - Used in app headers and horizontal navigation layouts to separate adjacent elements

**Technical behavior:**
- Built on Radix UI Separator with proper ARIA semantics
- Renders as semantic HTML separator element
- Uses design system color tokens for consistent styling
- Automatically applies appropriate ARIA attributes based on usage

### Usability

**Usage patterns:**
- **Default implementation** - Most usage is simply `<Divider />` with no props needed
- **Header separations** - Vertical orientation used in app headers to separate logo from workspace picker
- **Form organization** - Integrated into FormGroup component with `showDivider` prop control
- **Panel sections** - Separates different content areas within cards, panels, or modals

**Layout considerations:**
- Parent components typically handle spacing around dividers
- Dividers rely on container spacing rather than custom margins
- Used systematically in form groups and panel layouts for consistency
- Maintains visibility at different zoom levels and screen sizes

**User expectations:**
- Dividers provide clear visual breaks without interfering with content flow
- Consistent application across similar interface patterns
- Subtle visual presence that enhances rather than dominates content structure

### Content

**Integration patterns:**
- **FormGroup component** - Includes built-in divider with `showDivider` prop (defaults to `true`)
- **Panel and card layouts** - Used to separate content sections within containers
- **Header layouts** - Used with vertical orientation to separate navigation elements

**Spacing guidelines:**
- **Automatic spacing** - Parent components handle appropriate spacing around dividers
- **No manual margins** - Avoid adding custom margins; rely on container spacing patterns
- **Consistent application** - Follow established patterns in similar interface contexts

**Content organization:**
- Use dividers to create logical content groupings
- Apply consistently within similar interface patterns
- Maintain clear content hierarchy with appropriate visual breaks

### Accessibility

**What the design system provides:**
- Semantic separator role announced as "separator" to assistive technologies
- Proper ARIA attributes for screen reader compatibility
- High contrast support that works with system preferences
- Scalable design that maintains visibility at different zoom levels
- Non-interactive element that doesn't interfere with keyboard navigation flow

**Development responsibilities:**
- Use dividers appropriately to enhance content structure for screen readers
- Ensure dividers don't create confusion in content flow
- Test that dividers provide meaningful content organization for assistive technology users
- Verify that divider usage doesn't break logical reading order

**Design responsibilities:**
- Design sufficient contrast ratios for divider visibility
- Ensure dividers enhance rather than clutter interface organization
- Consider how dividers work with high contrast and reduced motion settings
- Design consistent divider usage patterns across similar interface contexts
