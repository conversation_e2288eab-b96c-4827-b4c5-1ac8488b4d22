import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ImageUploadFieldStories from './ImageUploadField.stories';

<Meta of={ImageUploadFieldStories} />

<Title />

<Description />

<Primary />

<Controls of={ImageUploadFieldStories.Playground} />

## Import

```jsx
import { ImageUploadField } from '@cosmos-lab/components/image-upload-field';
```

## 🟢 When to use the component

- **Image documentation** - For uploading screenshots, certificates, or visual evidence that benefits from preview functionality
- **Single image uploads** - When only one image file is needed with integrated preview and validation
- **Form-based image input** - When image upload is part of a larger form with validation and submission requirements

## ❌ When not to use the component

- **Multiple image uploads** - Use FileUploadField with image formats when multiple images are needed
- **Non-image files** - Use FileUploadField for documents, PDFs, or other non-image file types
- **Temporary image handling** - Use simpler patterns when images don't need persistent storage
- **Read-only image display** - Use Image component for displaying existing images without upload functionality

## 🛠️ How it works

The ImageUploadField component provides image upload functionality with preview capabilities, drag-and-drop support, and form field integration specifically optimized for single image uploads.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **Preview container** - Fixed-size preview area (16x dimension) with rounded corners and border styling
- **FileUpload integration** - Uses FileUpload component configured for single image files with hidden file list
- **Horizontal layout** - Side-by-side arrangement of preview and upload controls for optimal user experience

**Image handling:**
- **Single image only** - Configured with `isMulti={false}` for one image at a time
- **Accepted formats** - Supports common image formats (jpg, jpeg, png, gif, svg) defined in `ACCEPTED_FORMATS`
- **Size validation** - Default 25MB limit configurable via `maxFileSizeInBytes` prop
- **Preview generation** - Automatic preview using `URL.createObjectURL()` for immediate visual feedback
- **Memory management** - Proper cleanup of object URLs to prevent memory leaks

**Preview functionality:**
- **Fixed dimensions** - 16x16 design token sizing (256px) for consistent preview appearance
- **Object-fit cover** - Images are cropped to fit preview container while maintaining aspect ratio
- **Placeholder state** - Gray background when no image is selected
- **Border styling** - Consistent border radius and color using design tokens
- **Responsive preview** - Preview updates immediately when new image is selected

**Drag-and-drop support:**
- **Drop zone enabled** - `showDropzone={true}` provides drag-and-drop functionality
- **Visual feedback** - Clear indication of drag states and file acceptance
- **File list hidden** - `showFileList={false}` since preview serves as file confirmation
- **Error handling** - Validation errors displayed through FormField feedback system

**Form integration:**
- **Help text generation** - Automatic help text showing size limit and accepted formats when not provided
- **Validation feedback** - Integration with FormField for consistent error display
- **ARIA relationships** - Proper accessibility connections between preview, upload area, and labels
- **Required field support** - Standard form field required/optional handling

### Usability

**Upload experience:**
- **Visual confirmation** - Immediate preview provides clear confirmation of selected image
- **Multiple input methods** - Users can drag-and-drop, click to browse, or use keyboard navigation
- **Clear expectations** - Button text and inner label communicate upload functionality
- **Error recovery** - Validation errors provide guidance for resolution

**Preview interaction:**
- **Immediate feedback** - Preview updates instantly when image is selected
- **Size consistency** - Fixed preview dimensions provide predictable layout
- **Quality indication** - Users can see image quality and content before submission
- **Replace functionality** - Easy to replace image by selecting a new one

**Accessibility considerations:**
- **Screen reader support** - Preview image has proper alt text and ARIA relationships
- **Keyboard navigation** - Full keyboard support for upload functionality
- **Focus management** - Logical focus progression through upload interface
- **Error announcements** - Validation errors properly announced to assistive technology

### Content

**Field labeling:**
- **Descriptive labels** - Use clear labels that explain the image purpose (e.g., "Profile Photo", "Company Logo")
- **Action clarity** - Button text should clearly indicate upload action ("Upload image", "Select photo")
- **Context guidance** - Inner label should provide helpful context ("Or drop image here")
- **Help text usage** - Automatic help text shows file requirements, or provide custom context

**Image requirements:**
- **Format specification** - Component automatically shows accepted formats in help text
- **Size limitations** - Default help text includes size limit, or customize for specific needs
- **Quality guidance** - Use help text to explain image quality or dimension requirements
- **Business context** - Explain how the image will be used or displayed

**Error messaging:**
- **Format errors** - Clear messaging when unsupported file types are selected
- **Size errors** - Specific guidance when files exceed size limits
- **Validation feedback** - Integration with form validation for comprehensive error handling
- **Recovery guidance** - Clear path to resolve upload issues

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships and image preview labeling
- Full keyboard navigation support including Tab navigation and Space/Enter activation for upload controls
- Screen reader announcements for image selection, preview updates, and validation errors
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators and logical tab order through upload interface
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile interaction

**Development responsibilities:**
- Provide descriptive labels that give clear context for what image is being collected and its purpose
- Use meaningful help text that explains image requirements, size limits, format restrictions, and business context
- Implement proper error handling with clear, actionable messages for validation scenarios
- Ensure preview images have appropriate alt text that describes their purpose in the form context
- Handle loading states appropriately when image processing or validation is occurring
- Coordinate with form validation systems to provide consistent error handling and user feedback

**Design responsibilities:**
- Provide sufficient color contrast for preview container, upload controls, and validation feedback across themes
- Design clear visual hierarchy that shows the relationship between preview, upload area, and form labels
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for image upload fields across the application
- Design appropriate sizing for preview container and upload controls that work across different screen sizes
- Ensure drag-and-drop visual feedback and validation states provide clear visual cues that complement screen reader announcements

#### Keyboard Support

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <th>
                <kbd>Tab</kbd>
            </th>
            <th>Focus forward into the button</th>
        </tr>
    </tbody>
</table>