import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ShowMoreStories from './ShowMore.stories';

<Meta of={ShowMoreStories} />

<Title />

<Description />

<Primary />

<Controls of={ShowMoreStories.Playground} />

## Import

```jsx
import { ShowMore } from '@cosmos-lab/components/show-more';
```

## 🟢 When to use the component

- **Long content sections** - When text or content exceeds comfortable reading length and needs progressive disclosure
- **Space-constrained layouts** - When you need to preserve vertical space while keeping all content accessible
- **Content previews** - For showing truncated content with the option to expand for full details
- **Overflow management** - When content naturally exceeds the available display area

## ❌ When not to use the component

- **Critical information** - Don't hide essential information that users need to see immediately
- **Short content** - Avoid using for content that fits comfortably within the truncation height
- **Navigation or form elements** - Not appropriate for interactive elements that need constant access
- **Error messages or alerts** - Critical feedback should always be immediately visible

## 🛠️ How it works

The ShowMore component provides expandable content functionality with a gradient fade effect and accessible toggle controls for managing long content sections.

**Content and state management:**
- **Flexible content** - Accepts any ReactNode content with automatic truncation using design tokens
- **Internal state** - Manages its own expanded/collapsed state with optional initial state via `isOpen` prop
- **Null handling** - Returns `null` when no content is provided for graceful degradation

**Visual design:**
- **Fixed height truncation** - Uses `dimension32x` design token for consistent max-height constraint
- **Gradient overlay** - Creates smooth visual transition with `dimension6xl` height gradient using `SHOW_MORE_COLOR_STOP_GRADIENT_LINE` constant
- **Positioned controls** - Show/hide button is absolutely positioned at the bottom of the truncated content area

**Interaction patterns:**
- **Toggle functionality** - Single button switches between "Show more" and "Show less" states
- **Chevron indicators** - Uses ChevronDown/ChevronUp icons to indicate expand/collapse direction
- **Accessible controls** - Includes proper ARIA attributes (`aria-expanded`, `aria-controls`) for screen readers

### Usability

**Content organization:**
- **Meaningful previews** - Ensure truncated content provides valuable context within the fixed height constraint
- **Natural break points** - Consider content structure when the fixed truncation height cuts off text
- **Consistent behavior** - Use similar patterns across the application for predictable user experience

**Interaction patterns:**
- **Clear state indication** - Button labels and chevron icons clearly show current state and available action
- **Reversible actions** - Users can always collapse content back to truncated state
- **Touch-friendly controls** - Tertiary buttons with proper padding provide adequate touch targets

### Content

**Content guidelines:**
- **Complete information** - Full expanded content should be self-contained and directly related to the preview
- **Appropriate length** - Consider that truncation occurs at a fixed height regardless of content structure
- **Logical flow** - Maintain proper reading flow even when content is cut off at the truncation point

**Technical considerations:**
- **ReactNode flexibility** - Component accepts any valid React content including text, elements, and components
- **Height constraints** - Content is truncated using `overflow-y: hidden` with design token-based max-height
- **Gradient positioning** - Visual fade effect uses absolute positioning and linear gradient background

### Accessibility

**What the design system provides:**
- Semantic button structure with proper ARIA attributes (`aria-expanded="true/false"`, `aria-controls="show-more-content"`) for screen reader compatibility
- Keyboard navigation support with standard button focus management and activation patterns
- High contrast support and appropriate button styling that meets accessibility guidelines
- Descriptive button labels ("Show more"/"Show less") that clearly indicate functionality

**Development responsibilities:**
- Content structure to ensure logical reading order for screen readers when content expands and collapses
- Content relationships to maintain clear connections between truncated preview and full content
- Focus management to ensure smooth keyboard navigation experience during state changes
- Content quality to ensure truncated content provides meaningful context within the fixed height constraint

**Design responsibilities:**
- Visual hierarchy with sufficient contrast for gradient overlays and button elements against various background colors
- Responsive behavior that maintains usability and accessibility across different screen sizes and orientations
- Focus indicators that provide clear visual feedback for keyboard navigation and button interaction

