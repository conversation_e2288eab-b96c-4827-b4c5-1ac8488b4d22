import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as DateRangeFieldStories from './DateRangeField.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={DateRangeFieldStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="Date range validation (ensuring start date is before end date) only works when using controlled component values."
/>

<Description />

<Primary />

<Controls of={DateRangeFieldStories.Playground} />

## Import

```jsx
import { DateRangeField } from '@cosmos-lab/components/date-range-field';
```

## Examples with Pre-filled Values

The DateRangeField component is fully controlled and supports pre-filling date values using the `value` prop. This is useful for editing existing date ranges or providing suggested date ranges to users.

### With Pre-filled Start Date

You can pre-fill just the start date, leaving the end date for the user to select:

<Canvas of={DateRangeFieldStories.WithPrefilledStartDate} />

### With Pre-filled Date Range

You can pre-fill both the start and end dates:

<Canvas of={DateRangeFieldStories.WithPrefilledDateRange} />

## 🟢 When to use the component

- **Date period selection** - When users need to select a start and end date for reports, filters, or time-based queries
- **Validation with constraints** - When date ranges need validation (start before end) and unavailable date handling

## ❌ When not to use the component

- **Single date selection** - Use DatePickerField for selecting individual dates
- **Time-specific selections** - Use DateTimeField when specific times are required along with dates
- **Simple date inputs** - Use DatePickerField for straightforward single date requirements
- **Read-only date display** - Use DateTime component for displaying existing date ranges without selection

## 🛠️ How it works

The DateRangeField component provides dual date picker inputs for selecting start and end dates with validation, accessibility features, and unavailable date handling.

**Component structure:**
- **Dual DatePickerField inputs** - Separate start and end date pickers with coordinated behavior
- **Validation integration** - Built-in validation ensuring start date is before or equal to end date
- **Calendar coordination** - Start date selection influences available dates in end date picker
- **Form field wrapper** - Consistent labeling, help text, and validation feedback display
- **Accessibility layer** - Proper ARIA relationships and screen reader support for dual inputs

**Date validation:**
- **Range validation** - Automatically validates that start date is before or equal to end date
- **Controlled validation** - Validation only works when using controlled component values (not defaultValue)
- **Custom unavailable dates** - Support for `getIsDateUnavailableStart` and `getIsDateUnavailableEnd` functions
- **Cross-field constraints** - Start date selection automatically constrains available end dates

**Value management:**
- **Controlled component** - Uses `value` prop with `{ start: TDateISODate | null, end: TDateISODate | null }` structure
- **ISO date format** - All dates use ISO 8601 date strings (YYYY-MM-DD) with no time components
- **Null handling** - Supports partial selections where start or end can be null independently
- **Change handling** - `onChange` callback receives complete dual input object with both dates

**Unavailable date handling:**
- **Separate functions** - `getIsDateUnavailableStart` and `getIsDateUnavailableEnd` for independent constraints
- **Custom messages** - `dateUnavailableText` object with separate messages for start and end fields
- **Dynamic constraints** - Start date selection can influence end date availability through validation logic
- **Accessibility support** - Unavailable dates are properly announced to screen readers

### Usability

**Date selection flow:**
- **Logical progression** - Users typically select start date first, then end date with appropriate constraints
- **Visual feedback** - Clear indication of selected dates and validation states
- **Error prevention** - Unavailable dates are disabled rather than allowing invalid selections
- **Flexible interaction** - Users can modify either date independently while maintaining validation

**Range validation:**
- **Immediate feedback** - Validation errors appear as soon as invalid range is detected
- **Clear messaging** - Error messages explain the specific validation issue (start after end, etc.)
- **Prevention over correction** - Interface prevents invalid selections when possible
- **Recovery guidance** - Clear path to resolve validation errors through date adjustment

**Calendar behavior:**
- **Coordinated calendars** - Start date selection influences end date calendar constraints
- **Consistent placement** - Both calendars use same `calendarPlacement` for predictable positioning
- **Keyboard navigation** - Full keyboard support for both date pickers with logical tab order
- **Mobile optimization** - Touch-friendly interaction with appropriate calendar sizing

### Content

**Field labeling:**
- **Descriptive labels** - Use clear labels that explain the date range purpose (e.g., "Audit Period", "Report Range")
- **Hidden field labels** - Use `a11yHiddenLabel` to provide specific context for start and end fields
- **Contextual help** - Help text should explain any date constraints, validation rules, or business logic
- **Clear requirements** - Indicate when both dates are required vs. when partial ranges are acceptable

**Date range context:**
- **Business meaning** - Labels should reflect the business context (audit period, project timeline, etc.)
- **Constraint explanation** - Help text should explain any unavailable dates or business rules
- **Format consistency** - Use consistent date formatting and terminology across similar range fields
- **Validation messaging** - Error messages should clearly explain range validation requirements

**Accessibility labels:**
- **Individual field context** - `a11yHiddenLabel` provides specific context for each date picker
- **Screen reader clarity** - Labels should make the dual-input nature clear to assistive technology users
- **Action descriptions** - Clear labeling for any clear or reset functionality
- **Validation announcements** - Error messages should be announced appropriately to screen readers

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships and dual input coordination
- Full keyboard navigation support including Tab order between start/end fields and within calendars
- Screen reader announcements for date selection, validation errors, and range completion
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators and logical progression between date inputs
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile calendar interaction

**Development responsibilities:**
- Provide descriptive `label` and `a11yHiddenLabel` props that give clear context for the date range purpose
- Use meaningful help text that explains date constraints, business rules, or validation requirements
- Implement proper validation with clear, actionable error messages for range validation failures
- Ensure `getIsDateUnavailableStart` and `getIsDateUnavailableEnd` functions have logical, explainable constraints
- Handle loading and error states appropriately when date constraints depend on external data
- Provide consistent date formatting and terminology across similar date range implementations

**Design responsibilities:**
- Provide sufficient color contrast for all field states, calendar elements, and validation feedback across themes
- Design clear visual hierarchy that shows the relationship between start/end fields and overall range selection
- Ensure focus indicators are clearly visible and meet contrast requirements for both date inputs and calendar navigation
- Create consistent visual patterns for date range fields across the application
- Design appropriate spacing and sizing for dual date inputs, calendar positioning, and touch targets across screen sizes
- Ensure validation states and error messaging provide clear visual cues that complement screen reader announcements

