import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ListStories from './ListCosmosLab.stories';

<Meta of={ListStories} />

<Title />

<Description />

<Primary />

<Controls of={ListStories.Playground} />

## Import

```jsx
import { List } from '@cosmos-lab/components/list';
// or
import { ListRoot, ListItem } from '@cosmos-lab/components/list';
```

## Compound Component Pattern

The List component can be used as compound components, which gives you more control over the rendering of each item. This pattern also allows for creating nested lists, as shown in the example below.

<Canvas of={ListStories.CompoundComponent} />

## React Nodes

The List component can accept React nodes as list items, allowing you to include interactive elements like links within list items.

<Canvas of={ListStories.ReactNodes} />

## 🟢 When to use the component

- **Simple collections** - When you need to display a straightforward list of related items without complex structure
- **Semantic content** - For content that benefits from proper HTML list semantics (`<ul>`, `<ol>`, `<li>`)
- **Mixed content types** - When list items contain a combination of text, links, buttons, or other React components
- **Hierarchical information** - For nested lists that show relationships between different levels of content
- **Consistent styling** - When you need uniform typography and spacing across list items

## ❌ When not to use the component

- **Tabular data** - Use DataTable for data that requires column headers, sorting, or filtering
- **User management interfaces** - Use StackedList for structured data with avatars, metadata, and actions
- **Expandable content sections** - Use Accordion when items need to show/hide detailed information
- **Key-value information** - Use Key Value Pairs for displaying labeled facts or metadata

## 🛠️ How it works

The List component renders semantic HTML lists (`<ul>`, `<ol>`) with consistent styling and accessibility features, supporting both simple array-based and compound component patterns.

**List types:**
- **Unordered** - Renders as `<ul>` with bullet points for general collections of related items
- **Ordered** - Renders as `<ol>` with numbers for sequential or prioritized content
- **None** - Renders as `<ul>` with `list-style: none` for custom styling without default list markers

**Content handling:**
- **ReactNode items** - Accepts any valid React content including text, elements, and components
- **Direct rendering** - Items are passed directly to ListItem components without automatic text wrapping
- **Key generation** - Uses array index as fallback for React keys with `list-item-${index}` pattern

**Styling system:**
- **Size inheritance** - Uses Text component sizes (100, 200, 300, etc.) applied at the list level
- **Color schemes** - Supports all Text component color schemes (neutral, faded, etc.) for semantic meaning
- **Responsive design** - Maintains proper spacing and readability across different screen sizes

**Compound component pattern:**
- **ListRoot** - Container component that renders the appropriate HTML list element with styling props
- **ListItem** - Individual item component that renders `<li>` elements with proper styling and spans content
- **Nested lists** - Supports creating hierarchical content by nesting ListRoot components within ListItem components

**Technical implementation:**
- **Data attributes** - Automatically generates `data-id` attributes with pattern `${dataId}-list-item` for each item
- **Test integration** - Includes `data-testid="List"` on root element and `data-testid="ListItem"` on each item
- **Semantic markup** - Renders proper HTML list elements for accessibility and SEO

### Usability

**Content patterns:**
- **Consistent item types** - Consider using similar content types within a single list for predictable user experience
- **Appropriate nesting** - Use nested lists sparingly and ensure they enhance rather than complicate content structure
- **Element choice** - Choose list type (ordered/unordered/none) based on the semantic meaning of your content

**Performance considerations:**
- **Array index keys** - Component uses array index as fallback for React keys, which is acceptable for static lists but consider unique IDs for dynamic content
- **Content complexity** - Be mindful that complex ReactNode content in list items may impact rendering performance

### Content

**Content guidelines:**
- **Related items** - Ensure all items in a list are logically related and belong together
- **Consistent formatting** - Maintain similar content structure across list items for scannable content
- **Appropriate length** - Consider list length and whether long lists might benefit from pagination or grouping

**List type usage:**
- **Ordered lists** - Use for sequential steps, rankings, or prioritized content where order matters
- **Unordered lists** - Use for collections where order is not significant
- **None styling** - Use when you need semantic list structure but want custom visual styling

**Nested structure:**
- **Logical hierarchy** - Ensure nested lists represent genuine sub-relationships in your content
- **Consistent depth** - Avoid overly deep nesting that becomes difficult to follow
- **Clear relationships** - Make parent-child relationships obvious through content and styling

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper list elements (`<ul>`, `<ol>`, `<li>`) for screen reader navigation
- High contrast support that works with system preferences and meets WCAG guidelines
- Scalable typography that works with browser zoom and accessibility settings
- Proper color contrast ratios for all color scheme options

**Development responsibilities:**
- Content structure to organize list items logically for screen readers and assistive technology
- List type selection to use appropriate semantic markup (ordered vs unordered) based on content meaning
- Nested list structure to ensure hierarchical relationships are clear and navigable
- Interactive content to ensure any buttons, links, or interactive elements within list items are properly accessible

**Design responsibilities:**
- Visual hierarchy that supports the semantic structure with appropriate sizing and spacing
- Sufficient contrast for all list content and styling across color schemes
- Responsive typography that maintains readability and list structure across different screen sizes
- Focus indicators for any interactive content within list items

