import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as BreadcrumbsStories from './Breadcrumbs.stories';

<Meta of={BreadcrumbsStories} />

<Title />

<Description />

<Primary />

<Controls of={BreadcrumbsStories.Playground} />

## Import

```jsx
import { Breadcrumbs } from '@cosmos-lab/components/breadcrumbs';
```

## 🟢 When to use the component

- **Page hierarchy display** - To show the hierarchy and location of the current page within the application structure
- **Secondary navigation** - As a type of secondary navigation that reveals the user's location in an application
- **Most application pages** - Highly encouraged for the majority of pages, with exceptions for high-level overviews and dedicated workflows
- **Deep navigation paths** - For multi-level navigation where users might want to jump back to previous levels

## ❌ When not to use the component

- **Outside page headers** - Don't use as links outside of the page header area
- **Within body text** - Consider using Link component instead when links are needed within content
- **Standalone links** - Not appropriate as isolated navigation elements
- **High-level overviews** - Skip on top-level dashboard or overview pages where hierarchy isn't relevant
- **Dedicated workflows** - Avoid in linear processes or wizards where breadcrumb navigation might disrupt the flow

## 🛠️ How it works

The Breadcrumbs component displays hierarchical navigation links to help users understand their current location within a website's structure and navigate between levels.

**Navigation structure:**
- **Breadcrumb array** - Takes an array of `Breadcrumb` objects with `label` and `pathname` properties
- **All items interactive** - Every breadcrumb item is rendered as a clickable link or button
- **Trailing separators** - Renders slash icons after each breadcrumb segment (including the last one)

**Interaction patterns:**
- **Link navigation** - By default, renders `AppLink` components for standard navigation using the `pathname` property
- **Custom handlers** - When `onClick` prop is provided, renders `Button` components instead and calls `onClick(pathname)`
- **Consistent styling** - Uses small tertiary primary buttons or small AppLinks for uniform appearance

**Technical features:**
- **Semantic markup** - Renders within a `<nav>` element with proper `aria-label="Breadcrumb"`
- **Test integration** - Includes configurable `data-id` attributes with segment-specific IDs (`${dataId}-${label}BreadcrumbSegment`)
- **Icon integration** - Uses Slash icons with size="100" and colorScheme="faded" after each item

### Usability

**Navigation patterns:**
- **Logical hierarchy** - Ensure breadcrumbs reflect the actual information architecture of your application
- **All levels clickable** - Unlike traditional breadcrumbs, all items including the current page are interactive
- **Consistent interaction** - Choose either link-based or click handler-based navigation consistently within your application

**Current implementation considerations:**
- **No truncation support** - The component does not include built-in truncation features for long breadcrumb trails
- **Trailing slash** - Be aware that a slash icon appears after every breadcrumb item, including the last one
- **Manual current page handling** - You'll need to handle current page styling or behavior in your implementation

### Content

**Content guidelines:**
- **Clear labeling** - Use concise, descriptive labels that clearly identify each navigation level
- **Valid pathnames** - Ensure all `pathname` values are valid routes or identifiers for your click handler
- **Logical progression** - Each breadcrumb should represent a navigable location in your application hierarchy

**Data structure:**
- **Required properties** - Each breadcrumb object must have both `label` (string) and `pathname` (string) properties
- **Consistent data** - Maintain consistent data structure across all breadcrumb items in the array

### Accessibility

**What the design system provides:**
- Semantic navigation structure with proper `<nav>` element and `aria-label="Breadcrumb"` for screen reader identification
- Keyboard navigation support through standard link and button focus patterns
- High contrast support through faded color scheme for separator icons
- Proper focus indicators for keyboard navigation through breadcrumb links and buttons

**Development responsibilities:**
- Navigation structure to ensure breadcrumbs reflect logical page hierarchy for screen readers
- Link destinations to ensure all breadcrumb pathnames lead to accessible, meaningful pages
- Click handler implementation to provide appropriate navigation behavior when using custom onClick
- Current page indication to provide visual or semantic cues about the user's current location (not built into component)

**Design responsibilities:**
- Visual hierarchy that positions breadcrumbs appropriately within page headers
- Sufficient contrast for breadcrumb links and separator icons against background colors
- Current page styling to help users understand their location within the breadcrumb trail
- Responsive behavior for handling breadcrumb display on smaller screens

