import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as PanelSectionStories from './PanelSection.stories';

<Meta of={PanelSectionStories} />

<Title />

<Description />

<Primary />

<Controls of={PanelSectionStories.Playground} />

## Import

```jsx
import { PanelSection } from '@cosmos-lab/components/panel-section';
```

## 🟢 When to use the component

- **Panel content organization** - When you need to structure content within panels with clear sections and titles
- **Grouped information display** - For organizing related information under descriptive headings within panel layouts
- **Action-oriented sections** - When sections need associated actions like edit, delete, or expand functionality
- **Consistent panel layouts** - To maintain visual consistency across different panel implementations
- **Contextual information** - When sections need tooltips or additional context for user understanding
- **Flexible content areas** - For sections that need custom start/end slots for icons, badges, or other elements

## ❌ When not to use the component

- **Simple content blocks** - Use basic layout components like Box or Stack for content that doesn't need section structure
- **Full-page layouts** - Use page-level components for main content areas outside of panels
- **Navigation sections** - Use dedicated navigation components for menu or navigation content
- **Form organization** - Use FormGroup or form-specific components for organizing form fields
- **Card-based layouts** - Use Card component when content needs its own container outside of panel contexts

## 🛠️ How it works

The PanelSection component provides a structured section within panels with title, body content, optional borders, and customizable slots for consistent panel layouts.

**Content structure:**
- **Title area** - Contains the main title with optional tooltip support and customizable start/end slots
- **Action area** - Optional ActionStack for section-specific actions displayed on the right
- **Body content** - Flexible ReactNode content area with consistent padding
- **Border control** - Boxttom border for visual separation between sections

**Layout behavior:**
- **Consistent spacing** - Uses design system tokens for padding and spacing consistency
- **Flexible slots** - Start and end slots allow for custom content like icons, badges, or status indicators
- **Action integration** - Seamlessly integrates with ActionStack component for consistent action patterns
- **Responsive design** - Adapts to different panel widths while maintaining readability

**Visual features:**
- **Tooltip support** - Title can include tooltip text for additional context
- **Action alignment** - Actions are right-aligned and properly spaced from title content
- **Content padding** - Body content uses consistent padding (3xl bottom) for visual rhythm

### Usability

**Content organization:**
- **Clear hierarchy** - Title provides clear section identification and context
- **Scannable layout** - Consistent structure helps users quickly understand panel content organization
- **Action accessibility** - Section actions are clearly associated with their content areas
- **Progressive disclosure** - Tooltips provide additional context without cluttering the interface

**Layout patterns:**
- **Section stacking** - Multiple PanelSections can be stacked with visual separation between them
- **Action grouping** - Related actions are grouped together in the section header area
- **Content flexibility** - Body content can contain any React components or layouts
- **Visual separation** - Use `showBorderBottom={true}` (default) to create dividers between sections
- **Last section styling** - The final PanelSection in a stack should have `showBorderBottom={false}` to avoid unnecessary visual clutter at the bottom
- **Single section styling** - When using only one PanelSection, set `showBorderBottom={false}` as no separation is needed

### Content

**Title guidelines:**
- **Descriptive labels** - Use clear, concise titles that describe the section content
- **Consistent terminology** - Maintain consistent language across similar sections
- **Contextual tooltips** - Provide additional context through tooltips when section purpose isn't immediately clear

**Body content:**
- **Focused information** - Keep section content focused on a single topic or related group of information
- **Scannable format** - Organize content for quick comprehension within the panel context
- **Appropriate depth** - Balance detail level with panel space constraints

**Action design:**
- **Relevant actions** - Include only actions that directly relate to the section content
- **Clear labels** - Use action-oriented labels that clearly describe what will happen
- **Appropriate hierarchy** - Use button levels that reflect action importance within the section context

**Slot usage:**
- **Start slot** - Use for status indicators, icons, or badges that provide immediate context
- **End slot** - Use for secondary information, counts, or supplementary indicators
- **Consistent patterns** - Maintain consistent slot usage patterns across similar sections

### Accessibility

**What the design system provides:**
- Semantic structure with proper heading hierarchy and content organization for screen readers
- Keyboard navigation with full keyboard accessibility for all interactive elements including actions and tooltips
- Screen reader support through proper labeling and content structure
- Focus management with logical tab order through section elements
- Touch targets with adequate sizing for interactive elements on mobile devices

**Development responsibilities:**
- Proper heading structure to ensure section titles maintain appropriate heading hierarchy within panel context
- Action accessibility to verify all section actions have appropriate labels and keyboard support
- Tooltip implementation to ensure tooltip content is accessible to screen readers
- Content organization to structure section content logically for assistive technology
- Focus handling to manage focus appropriately when section actions are triggered

**Design responsibilities:**
- Visual hierarchy with clear relationships between title, actions, and body content
- Sufficient contrast to ensure all text and interactive elements meet accessibility requirements
- Focus indicators to provide clear focus states for all interactive elements
- Responsive behavior to ensure sections work effectively across different screen sizes
- Content overflow handling to manage long titles and content appropriately

