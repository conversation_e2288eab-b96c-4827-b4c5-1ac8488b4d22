import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ChoiceCardGroupStories from './ChoiceCardGroup.stories';

<Meta of={ChoiceCardGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={ChoiceCardGroupStories.Playground} />

## Import

```jsx
import { ChoiceCardGroup } from '@cosmos-lab/components/choice-card-group';
```

## 🟢 When to use the component

- **Related option sets** - When grouping multiple ChoiceCards that belong to the same decision or category
- **Form field replacement** - When standard radio or checkbox groups need enhanced visual presentation
- **Complex selection scenarios** - When users need to compare multiple options with detailed information

## ❌ When not to use the component

- **Simple form fields** - Use standard RadioFieldGroup or CheckboxFieldGroup for basic selections
- **Space-constrained layouts** - Groups require significant space
- **Unrelated options** - Don't group ChoiceCards that represent different decisions

## 🛠️ How it works

Groups multiple choice cards together with shared validation and selection behavior for radio or checkbox interactions. Automatically manages orientation, focus, and accessibility for the entire group.

#### Usage guidelines

- Use ChoiceCardGroup to manage related ChoiceCard selections
- Supports both single selection (radio) and multiple selection (checkbox) modes
- Provides automatic responsive layout switching between horizontal and vertical orientations
- Handles form validation and error states at the group level

#### Using input types

**Checkbox type**
- **Definition:** Allows multiple selections from the group of options
- **Purpose:** When users can select zero, one, or multiple items

**Radio type**
- **Definition:** Allows only one selection from the group of mutually exclusive options
- **Purpose:** When users must choose exactly one option from the available choices

### Usability

**Orientation behavior:**
- Automatically switches to vertical layout when more than 3 options are provided
- Horizontal layout used for 3 or fewer options when space allows
- Can be manually overridden using `cosmosUseWithCaution_forceOptionOrientation`
- Responsive behavior adapts to container width

**Selection management:**
- Handles value state for the entire group
- Provides onChange callback with all selected values
- Supports controlled and uncontrolled usage patterns
- Manages focus movement between options

**Keyboard navigation:**
- **Tab** - Navigate into and out of the group
- **Arrow keys** - Move between options within the group (radio type)
- **Space** - Toggle selection for focused option
- **Enter** - Alternative activation method

### Content

**Group labeling:**
- Use clear, descriptive labels for the entire group
- Provide help text to explain the selection criteria
- Use parallel structure across all options in the group
- Keep individual option labels concise and scannable

**Option organization:**
- Order options logically (alphabetical, by importance, or by frequency of use)
- Group related sub-options together
- Consider the visual weight and balance of option content
- Maintain consistent content patterns across all options

**Help text guidelines:**
- Explain what the selection affects or controls
- Clarify whether selection is required or optional
- Provide context for complex decisions
- Use sentence case and clear, simple language

### Accessibility

**What the design system provides:**
- Proper fieldset and legend structure for screen readers
- ARIA attributes for group labeling and descriptions
- Keyboard navigation support with arrow key movement
- Focus management within the group
- Screen reader announcements for selection changes
- High contrast mode compatibility
- Touch target sizing meets accessibility guidelines

**Development responsibilities:**
- Associate the group with form validation and error messaging
- Provide clear error feedback at the group level
- Ensure proper form submission handling
- Test keyboard navigation across different browsers
- Implement accessible loading states for dynamic options

**Design responsibilities:**
- Design clear visual grouping that works for screen readers
- Ensure sufficient contrast for all group elements
- Create accessible error states that are clearly associated with the group
- Don't rely solely on color to convey group state or validation
- Maintain adequate spacing between options to prevent accidental selection
