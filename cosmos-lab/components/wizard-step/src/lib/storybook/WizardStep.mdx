import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as WizardStepStories from './WizardStep.stories';

<Meta of={WizardStepStories} />

<Title />

<Description />

<Primary />

<Controls of={WizardStepStories.Playground} />

## Import

```jsx
import { WizardStep } from '@cosmos-lab/components/wizard-step-cosmos-lab';
```

## 🟢 When to use the component

- **Multi-step processes** - When displaying individual steps within a wizard or stepped workflow

## ❌ When not to use the component

- **Standalone indicators** - Use other progress indicators for non-wizard contexts
- **Complex branching** - When the workflow has complex conditional paths that don't follow a linear progression

## 🛠️ How it works

The WizardStep component represents an individual step within a wizard, displaying step number, title, and completion status with visual indicators.

**Component structure:**
- **Step indicator** - Circular element showing step number or completion checkmark
- **Step content** - Title and optional subtitle text
- **Connector line** - Vertical line connecting to the next step (when `hasNextStep` is true)

**Visual states:**
- **Upcoming** - Muted styling with step number, indicating future steps
- **Active** - Emphasized styling with step number, showing the current step
- **Complete** - Success styling with checkmark icon, indicating finished steps

**Content display:**
- **Step numbering** - Automatically displays the provided step number starting from 1
- **Title text** - Primary step label with appropriate color based on step state
- **Subtitle text** - Optional secondary text for additional context
- **Connector logic** - Shows vertical connector line only when `hasNextStep` is true

### Usability

**Step progression:**
- **Clear visual hierarchy** - Different states (upcoming, active, complete) are visually distinct
- **Logical numbering** - Steps display with sequential numbering starting from 1
- **Connection indication** - Vertical connectors show the relationship between sequential steps
- **State communication** - Visual indicators clearly communicate step status to users

**Content organization:**
- **Concise titles** - Step titles should be brief and descriptive
- **Optional context** - Use subtitles when additional context helps users understand the step
- **Consistent styling** - All steps maintain consistent visual treatment within the wizard

### Content

**Step titles:**
- **Clear and concise** - Use brief, descriptive titles that clearly indicate what each step accomplishes
- **Action-oriented** - Focus on what the user will do or accomplish in each step
- **Consistent terminology** - Maintain consistent language patterns across all steps

**Step subtitles:**
- **Additional context** - Use when users need more information to understand the step's purpose
- **Brief descriptions** - Keep subtitles short and focused on essential information
- **Optional usage** - Only include when they add meaningful value to user understanding

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper text hierarchy and color contrast
- Visual state indicators that work with high contrast mode and system preferences
- Scalable typography that works with browser zoom and accessibility settings
- Clear visual distinction between different step states for users with visual impairments

**Development responsibilities:**
- Ensure step content is meaningful and descriptive for screen reader users
- Provide proper context when steps are part of a larger wizard workflow
- Handle dynamic step state changes appropriately for assistive technology
- Maintain logical step numbering and progression for screen reader navigation

**Design responsibilities:**
- Provide sufficient color contrast between different step states and background
- Ensure step indicators and text remain readable across different screen sizes
- Design clear visual hierarchy that supports the step progression narrative
- Maintain consistent spacing and alignment for optimal readability

