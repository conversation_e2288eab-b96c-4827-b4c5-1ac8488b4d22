import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as ComboboxFieldStories from './ComboboxField.stories';

<Meta of={ComboboxFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<br />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={ComboboxFieldStories.Playground} />

## Import

```jsx
import { ComboboxField } from '@cosmos/components/combobox-field';
```

## Props

### `defaultValue`

Passing an option object to `defaultValue` will set that option as the initially selected value.

<Canvas of={ComboboxFieldStories.DefaultValue} />

## Examples

### Multi Select

Enable with the `isMultiSelect` prop. When enabled, `getRemoveIndividualSelectedItemClickLabel` and `removeAllSelectedItemsLabel` are required props.

Multi select works with Async search.

<Canvas of={ComboboxFieldStories.MultiSelect} />

## Troubleshooting Common Issues

### Why don't the checkboxes stay "checked" after selecting an item in a MultiSelect combobox?

Both issues stem from unstable function references or dependencies in your asynchronous operations. If you are seeing resets or multiple fetch triggers, it is likely that functions like `onChange` or `onFetchOptions` are being re-created on each render, causing unexpected behavior. To address this, you can:

- Use `useCallback` to stabilize function references, such as `onFetchOptions` and `onFilterOptions`.
- Ensure that the component’s state and props (e.g., `options`, `defaultSelectedOptions`, `paginationOptions`) are properly controlled and memoized to prevent re-renders from resetting the state or causing repeated fetch calls.

### How can I enable clearing the selection on Single Select combobox?

To enable clearing the selection on Single Select combobox, you need to set `clearSelectedItemButtonLabel` prop. This prop will render a clear button icon to the right of the selected item.

## 🟢 When to use the component

- **Searchable selections** - When users need to search through a list of options to find and select items
- **Large option sets** - For dropdown lists with many options that benefit from search filtering
- **Multi-select scenarios** - When users need to select multiple items with clear visual feedback via tags
- **Async data loading** - For options that are fetched from APIs based on search queries or user interaction
- **Form fields with search** - Standard form implementations that require search functionality within dropdowns
- **Dynamic option loading** - For scenarios requiring pagination, infinite scroll, or search-based option fetching

## ❌ When not to use the component

- **Simple dropdowns** - Use SelectField for basic single-selection without search functionality
- **Small option sets** - Use SelectField or RadioFieldGroup for 5 or fewer clearly distinct options
- **Binary choices** - Use CheckboxField or RadioFieldGroup for yes/no or either/or decisions
- **Static lists** - Use SelectField when options are static and don't require search functionality
- **Complex option layouts** - Use custom implementations when options need rich content beyond label/description

## 🛠️ How it works

The ComboboxField component combines FormField structure with Combobox functionality to provide a complete form input with search capabilities, validation, and accessibility features.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **Combobox core** - Handles search input, dropdown display, option selection, and keyboard navigation
- **Validation integration** - Connects with form validation systems and displays feedback appropriately
- **Accessibility layer** - Ensures proper ARIA relationships between label, input, help text, and feedback

**Selection modes:**
- **Single-select** - Use `defaultValue` prop with single ListBoxItemData object, `onChange` receives single item or undefined
- **Multi-select** - Enable with `isMultiSelect` prop, use `defaultSelectedOptions` array, `onChange` receives array of items
- **Tag display** - Multi-select shows selected items as removable tags below the input field
- **Clear functionality** - Optional clear button for single-select, clear-all functionality for multi-select

**Data loading patterns:**
- **Static options** - Pass complete `options` array for client-side filtering and selection
- **Async search** - Use `onFetchOptions` callback for server-side search and dynamic option loading
- **Lazy loading** - Enable `hasMore` and implement pagination in `onFetchOptions` for large datasets
- **Fetch controls** - Configure `disableFetchOnMount` and `enableFetchOnFocus` for optimal loading behavior

**Search and filtering:**
- **Debounced search** - Configurable `searchDebounce` (default 300ms) prevents excessive API calls
- **Search states** - Built-in loading indicators, empty states, and error handling for search operations
- **Custom empty states** - Use `getSearchEmptyState` to provide contextual messages when no results are found
- **Input persistence** - Search input remains visible and editable independent of selection state

### Usability

**Search experience:**
- **Immediate feedback** - Users see loading states and results update as they type
- **Clear expectations** - Placeholder text should indicate what users can search for
- **Persistent search** - Search input stays available for continued filtering after selection
- **Meaningful results** - Empty states should guide users on how to find what they're looking for

**Selection patterns:**
- **Single-select clarity** - Clear visual indication of selected item with optional clear button
- **Multi-select management** - Tags show selected items with individual remove buttons and bulk clear option
- **Keyboard efficiency** - Full keyboard navigation supports power users and accessibility needs
- **Touch optimization** - Adequate touch targets for mobile interaction with tags and options

**Performance considerations:**
- **Efficient searching** - Debounced search prevents overwhelming servers with rapid requests
- **Smart loading** - Configure fetch behavior to balance user experience with performance
- **Memory management** - Component handles cleanup of search requests and event listeners
- **Responsive behavior** - Dropdown positioning adapts to viewport constraints and available space

### Content

**Field labeling:**
- **Descriptive labels** - Use clear labels that explain what users are selecting
- **Helpful placeholders** - Placeholder text should guide users on search behavior and expected input
- **Contextual help** - Use help text to explain complex selection rules or provide additional context
- **Clear requirements** - Indicate when selections are required and any validation rules

**Option content:**
- **Scannable labels** - Use concise, descriptive labels that are easy to scan and differentiate
- **Consistent structure** - Maintain consistent option data structure across similar form fields
- **Search optimization** - Structure option labels and descriptions to work well with search functionality
- **Meaningful descriptions** - Use optional descriptions to provide additional context when helpful

**Multi-select labels:**
- **Tag clarity** - Selected items should display clearly as tags with obvious remove functionality
- **Bulk actions** - Provide clear labeling for "remove all" functionality when enabled
- **Individual removal** - Use descriptive labels for individual tag removal actions
- **Visual hierarchy** - Ensure tag display doesn't overwhelm the primary input field

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships, ARIA roles, and keyboard navigation support
- Full keyboard accessibility including arrow key navigation, Enter/Space selection, and Escape to close dropdown
- Screen reader announcements for search results, selection changes, loading states, and validation feedback
- High contrast support that works with system accessibility preferences and meets WCAG color contrast guidelines
- Focus management with visible focus indicators and logical tab order through input, options, and selected tags
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile and assistive technology interaction

**Development responsibilities:**
- Provide descriptive labels that give clear context for what users are selecting
- Ensure placeholder text is helpful but doesn't replace proper labeling for screen reader users
- Use meaningful help text that explains complex selection rules or provides necessary context
- Implement proper validation with clear, actionable error messages that are announced to screen readers
- Ensure `getRemoveIndividualSelectedItemClickLabel` provides descriptive text for tag removal actions
- Handle loading and error states with appropriate ARIA live region announcements

**Design responsibilities:**
- Provide sufficient color contrast for all field states, selected items, and validation feedback across different themes
- Design clear visual hierarchy that shows the relationship between label, input, selected items, and help text
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for similar combobox fields across the application
- Design appropriate spacing and sizing for touch targets, tag display, and dropdown positioning across screen sizes
- Ensure loading states, empty states, and validation feedback provide clear visual cues that complement screen reader announcements

