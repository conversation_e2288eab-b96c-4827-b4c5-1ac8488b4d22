import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as SelectFieldStories from './SelectField.stories';

<Meta of={SelectFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<Description />

<Primary />

<Controls of={SelectFieldStories.Playground} />

## Import

```jsx
import { SelectField } from '@drata/cosmos-select-field';
```

## Props

### `isLoading`

<Canvas of={SelectFieldStories.Loading} />

## Examples

### Default Value

<Canvas of={SelectFieldStories.DefaultValue} />

## 🟢 When to use the component

- **Standard dropdown selections** - When users need to choose one option from a short list of predefined choices
- **Form field requirements** - For selections that need labels, help text, validation feedback, and form integration
- **Settings and preferences** - When configuring options that require a single selection with clear labeling
- **Filter controls** - For filtering interfaces where users select one criterion from a dropdown

## ❌ When not to use the component

- **Multiple selections** - Use ComboboxField when users need to select multiple items
- **Searchable options** - Use ComboboxField when users need to search through options
- **Large option lists** - Use ComboboxField for lists with many options that benefit from search
- **Binary choices** - Use ToggleField or CheckboxField for simple on/off or yes/no decisions
- **Few options (2-4)** - Consider RadioFieldGroup for better visibility of all available choices
- **Building components** - Use the foundational Select component when creating new Cosmos form components

## 🛠️ How it works

The SelectField component combines FormField structure with Select functionality to provide a complete form input with dropdown selection, labeling, validation, and accessibility features.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **Select core** - Handles dropdown display, option selection, and keyboard navigation using Downshift
- **Validation integration** - Connects with form validation systems and displays feedback appropriately
- **Accessibility layer** - Ensures proper ARIA relationships between label, input, help text, and feedback

**Selection behavior:**
- **Single selection** - Users select exactly one option from the dropdown list
- **Controlled state** - Uses `value` prop with `onChange` callback for controlled selection
- **Default value** - Supports `defaultValue` prop for initial selection state
- **Placeholder support** - Shows placeholder text when no option is selected

**Form field features:**
- **Complete labeling** - Includes label, optional text, and help text with proper HTML relationships
- **Validation feedback** - Displays success, error, or warning messages with appropriate styling
- **Required field handling** - Visual indicators and validation for mandatory selections
- **Field width control** - Configurable width options for different layout needs

**Option management:**
- **ListBoxItems structure** - Accepts options as array supporting individual options and option groups
- **Loading states** - Shows spinner with customizable loader label during async operations
- **Disabled options** - Individual options can be disabled while maintaining group functionality
- **Empty state handling** - Gracefully handles scenarios with no available options

**Positioning and interaction:**
- **Intelligent dropdown placement** - Uses Floating UI for optimal positioning with viewport awareness
- **Keyboard navigation** - Full keyboard support with arrow keys, Enter/Space, and Escape
- **Focus management** - Proper focus handling between trigger and dropdown options
- **Touch optimization** - Appropriate touch targets for mobile interaction

### Usability

**Selection design:**
- **Clear option labels** - Each option should be descriptive and easy to understand
- **Logical ordering** - Arrange options by frequency of use, importance, or alphabetically
- **Reasonable option count** - Keep lists manageable (typically under 10-15 options)
- **Consistent structure** - Use parallel grammatical structure across all options

**Field labeling:**
- **Descriptive labels** - Use clear labels that explain what users are selecting
- **Helpful placeholders** - Placeholder text should guide users on what to select
- **Contextual help** - Use help text to explain selection criteria or provide additional context
- **Clear requirements** - Indicate when selections are required and any validation rules

**State management:**
- **Default selections** - Consider providing sensible default values when appropriate
- **Validation timing** - Validate selections at appropriate moments (on change, on blur, on submit)
- **Error recovery** - Provide clear error messages and guidance for invalid selections
- **Loading feedback** - Show loading states during async option fetching

**Layout considerations:**
- **Field width** - Choose appropriate field width based on expected option label lengths
- **Form context** - Consider SelectField placement within broader form layouts
- **Responsive behavior** - Ensure dropdown works well across different screen sizes
- **Visual hierarchy** - Use consistent styling with other form fields in the same context

### Content

**Option content:**
- **Descriptive labels** - Each option should clearly describe what it represents
- **Parallel structure** - Use consistent grammatical patterns across all options
- **Concise text** - Keep option labels brief while maintaining clarity
- **Meaningful values** - Ensure option values are appropriate for form processing

**Field labeling:**
- **Action-oriented labels** - Use labels that clearly indicate what users should select
- **Context-specific help** - Tailor help text to explain the specific selection context
- **Clear placeholders** - Use placeholder text that guides user action ("Select an option", "Choose...")
- **Validation messaging** - Provide specific, actionable error messages for validation failures

**Loading and empty states:**
- **Informative loading** - Use descriptive loader labels that explain what's being loaded
- **Empty state guidance** - Provide helpful messaging when no options are available
- **Error state recovery** - Include guidance on how to resolve loading or data errors
- **Progressive disclosure** - Consider showing most common options first when appropriate

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships using label elements and ARIA attributes
- Complete keyboard navigation including arrow keys for option selection and Escape to close dropdown
- Screen reader announcements for selection changes, validation feedback, and loading states
- High contrast support that works with system accessibility preferences and meets WCAG AA standards
- Focus management with visible focus indicators and logical tab order through field elements
- Automatic ARIA relationships connecting labels, help text, validation feedback, and dropdown options

**Development responsibilities:**
- Provide meaningful `label` prop that clearly describes the selection being made
- Use `helpText` prop to provide additional context or instructions when needed
- Implement proper `onChange` handling that updates form state when selection changes
- Use `required` prop appropriately when selection is mandatory for form submission
- Provide clear, descriptive option labels that work well with screen reader announcements
- Handle validation states appropriately with meaningful error messages in `feedback` prop

**Design responsibilities:**
- Provide sufficient color contrast for all field states including focus, error, and disabled states
- Design clear visual hierarchy that distinguishes between label, input, help text, and feedback
- Ensure focus indicators are clearly visible and meet contrast requirements for keyboard navigation
- Create consistent visual patterns for form fields that work across different contexts and layouts
- Design appropriate spacing and sizing that works with browser zoom up to 200% magnification
- Ensure validation states are clearly distinguishable through visual design and not rely solely on color

