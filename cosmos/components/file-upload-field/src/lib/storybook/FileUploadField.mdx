import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as FileUploadFieldStories from './FileUploadField.stories';

<Meta of={FileUploadFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<br />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>
<br />

<Description />

<Primary />

<Controls of={FileUploadFieldStories.Playground} />

## Import

```jsx
import { FileUploadField } from '@drata/cosmos-file-upload-field';
```

## 🟢 When to use the component

- **Document uploads** - When users need to upload files like PDFs, Word documents, or spreadsheets
- **Multi-file collection** - For scenarios requiring multiple file uploads with individual validation and management
- **Form-based file input** - When file upload is part of a larger form with validation and submission requirements
- **Drag-and-drop uploads** - When users benefit from drag-and-drop functionality alongside traditional file selection
- **File validation needs** - When uploads require format restrictions, size limits, or custom validation rules

## ❌ When not to use the component

- **Image-specific uploads** - Use ImageUploadField for image uploads with preview functionality
- **Large file transfers** - Use specialized upload components for very large files requiring progress indicators
- **Temporary file handling** - Use simpler patterns when files don't need persistent storage or validation
- **Read-only file display** - Use appropriate display components when files are only being shown, not uploaded

## 🛠️ How it works

The FileUploadField component provides comprehensive file upload functionality with form field integration, drag-and-drop support, validation, and file management capabilities.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **FileUpload core** - Handles drag-and-drop, file selection, validation, and file list management
- **Validation integration** - Supports field-level validation feedback and file-specific error handling
- **Accessibility layer** - Ensures proper ARIA relationships and keyboard navigation support

**File handling:**
- **Multiple formats** - Accepts array of `acceptedFormats` using supported MIME types (png, jpeg, pdf, csv, etc.)
- **Size validation** - Configurable `maxFileSizeInBytes` with default 25MB limit
- **Multi-file support** - `isMulti` prop enables multiple file selection and management
- **Single file mode** - `oneFileOnly` prop restricts to exactly one file when enabled
- **Initial files** - Supports `initialFiles` and `initialFilesWithErrors` for pre-populated states

**Drag-and-drop functionality:**
- **Drop zone display** - `showDropzone` prop controls visibility of drag-and-drop area
- **Visual feedback** - Clear visual indicators for drag states and file acceptance
- **File list management** - `showFileList` prop controls display of uploaded files
- **Error handling** - Separate handling for accepted files and files with validation errors

**Validation and feedback:**
- **Custom error messages** - `errorCodeMessages` object for file-invalid-type, file-too-large, file-too-small, too-many-files
- **Field-level feedback** - Integration with FormField feedback system for overall field validation
- **File-specific errors** - Individual file error display within the file list
- **Real-time validation** - Immediate feedback during file selection and drag-and-drop

**Event handling:**
- **Unified callback** - `onUpdate` callback provides comprehensive file state updates
- **Action tracking** - Callback includes action type (DROP_ACCEPTED, DROP_REJECTED, REMOVE_FILE)
- **Complete state** - Callback receives both valid files and files with errors
- **Change detection** - Includes information about newly added files and removed files

### Usability

**File selection experience:**
- **Multiple input methods** - Users can drag-and-drop, click to browse, or use keyboard navigation
- **Clear expectations** - Button text and inner label clearly communicate upload functionality
- **Visual feedback** - Immediate visual confirmation of file selection and validation status
- **Error recovery** - Clear error messages with guidance on resolving file issues

**File management:**
- **Individual control** - Users can remove specific files from multi-file selections
- **Validation clarity** - Clear distinction between valid files and files with errors
- **Progress indication** - Visual feedback during file processing and validation
- **Bulk operations** - Efficient handling of multiple file operations

**Accessibility considerations:**
- **Keyboard navigation** - Full keyboard support for file selection and management
- **Screen reader support** - Proper announcements for file selection, validation, and removal
- **Focus management** - Logical focus progression through upload interface
- **Error announcements** - Validation errors are properly announced to assistive technology

### Content

**Field labeling:**
- **Descriptive labels** - Use clear labels that explain what files are being collected (e.g., "Supporting Documents", "Compliance Evidence")
- **Action clarity** - Button text should clearly indicate the upload action ("Upload files", "Select documents")
- **Context guidance** - Inner label should provide helpful context ("Or drop files here")
- **Help text usage** - Use help text to explain file requirements, size limits, or format restrictions

**File requirements:**
- **Format specification** - Clearly communicate accepted file formats in help text or labels
- **Size limitations** - Inform users of file size limits before they attempt upload
- **Quantity limits** - Explain any restrictions on number of files when using multi-file mode
- **Business context** - Help text should explain why files are needed and how they'll be used

**Error messaging:**
- **Specific guidance** - Error messages should clearly explain what's wrong and how to fix it
- **Format errors** - "Not a valid file type" should be accompanied by list of accepted formats
- **Size errors** - "File size is too large" should include the size limit
- **Actionable solutions** - Error messages should guide users toward successful upload

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships, file input labeling, and drag-and-drop accessibility
- Full keyboard navigation support including Tab navigation, Space/Enter activation, and arrow key file management
- Screen reader announcements for file selection, validation errors, upload progress, and file removal actions
- High contrast support that works with system accessibility preferences and meets WCAG guidelines for all upload states
- Focus management with visible focus indicators and logical tab order through upload interface and file list
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile file selection and management

**Development responsibilities:**
- Provide descriptive labels that give clear context for what files are being collected and their purpose
- Use meaningful help text that explains file requirements, size limits, format restrictions, and business context
- Implement comprehensive error handling with clear, actionable messages for all validation scenarios
- Ensure `errorCodeMessages` provide specific, helpful guidance for each type of file validation error
- Handle loading and processing states appropriately with proper ARIA live region announcements
- Coordinate with form validation systems to provide consistent error handling and user feedback

**Design responsibilities:**
- Provide sufficient color contrast for all upload states, file list items, and validation feedback across different themes
- Design clear visual hierarchy that shows the relationship between upload area, file list, and validation messages
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for file upload fields across the application
- Design appropriate spacing and sizing for drag-and-drop areas, file list items, and touch targets across screen sizes
- Ensure drag-and-drop visual feedback, file validation states, and error messaging provide clear visual cues that complement screen reader announcements

