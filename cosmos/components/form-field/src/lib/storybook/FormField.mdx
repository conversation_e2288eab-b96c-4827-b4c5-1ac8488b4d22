import { Controls, Description, Meta, Primary, Title } from '@storybook/addon-docs/blocks';
import * as FormFieldStories from './FormField.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={FormFieldStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />

<br />

## Content Guidelines

<Primary />

<Controls of={FormFieldStories.Playground} />

## Import

```jsx
import { FormField } from '@drata/cosmos-form-field';
```

## 🟢 When to use the component

- **Building Cosmos form components** - Use as the foundational wrapper when creating new form field components for the design system
- **Form field composition** - Combine with FieldLabel, FieldFeedback, and input components to create complete form fields
- **Consistent form layouts** - Ensure uniform spacing, labeling, and feedback patterns across all form components
- **Accessibility compliance** - Provide proper ARIA relationships, labeling, and form field structure
- **Design system extension** - Create new form field types that maintain design system consistency

## ❌ When not to use the component

- **Application development** - Use complete form field components (TextField, SelectField, etc.) instead of building with FormField directly
- **Non-form contexts** - Use appropriate layout components for non-form content organization
- **Custom form systems** - This component is specifically designed for Cosmos design system patterns

## 🛠️ How it works

The FormField component provides foundational pre-configured spacing and structure for building other Cosmos input field components with consistent labeling, feedback, and accessibility patterns.

**Component structure:**
- **Flexible wrapper** - Uses configurable HTML elements (div, fieldset) based on `role` prop
- **Grid-based layouts** - Supports stack, input-left, and input-right layout patterns with CSS Grid
- **Label integration** - Conditionally renders FieldLabel with proper HTML relationships
- **Feedback system** - Integrates FieldFeedback for validation and help messaging
- **Input rendering** - Uses render prop pattern for flexible input component integration

**Layout system:**
- **Stack layout** - Default vertical layout with label above input and feedback below
- **Input-left layout** - Horizontal layout with input on left side of label
- **Input-right layout** - Horizontal layout with input on right side of label
- **Configurable gaps** - Separate gap controls for stack, input-left, and input-right layouts
- **Responsive behavior** - Layouts adapt appropriately across different screen sizes

**Accessibility features:**
- **Semantic HTML** - Uses appropriate wrapper elements (div vs fieldset) based on field type
- **ARIA relationships** - Proper `htmlFor`, `aria-describedby`, and `aria-labelledby` connections
- **Screen reader support** - Option to hide labels from screen readers when inputs provide their own labels
- **Role-based rendering** - Renders `<legend>` instead of `<label>` for radio groups and checkbox groups
- **ID management** - Automatic generation of unique IDs for proper form field relationships

**Field width options:**
- **Full-width** - Default behavior stretching to container width
- **Fit-content** - Shrinks to content size for compact layouts
- **Responsive adaptation** - Width behavior adapts to layout and content requirements

**Render prop integration:**
- **Flexible input rendering** - `renderInput` function receives all necessary props for input integration
- **Context passing** - Provides input ID, label ID, feedback type, and ARIA relationships
- **Grid positioning** - Passes `gridArea` for proper CSS Grid placement
- **Validation state** - Provides feedback type for input styling coordination

### Usability

**Layout selection:**
- **Stack layout** - Use for most form fields where vertical space is available
- **Input-right layout** - Use for checkboxes, toggles, and compact form arrangements
- **Input-left layout** - Use for specialized layouts where input precedes label context
- **Consistent patterns** - Maintain layout consistency within related form sections

**Label and help text:**
- **Required vs optional** - Automatic handling of required/optional text based on `required` prop
- **Help text integration** - Seamless integration of help text with proper ARIA relationships
- **Label hiding** - Support for visually hidden labels when inputs provide sufficient context
- **Legend rendering** - Automatic legend rendering for grouped inputs (radio, checkbox groups)

**Feedback integration:**
- **Validation states** - Automatic feedback display based on validation results
- **Error messaging** - Clear error message display with proper ARIA announcements
- **Success feedback** - Optional success state feedback for positive validation
- **Feedback timing** - Feedback appears and disappears based on validation state changes

### Content

**Label guidelines:**
- **Descriptive labels** - Use clear, specific labels that explain the field's purpose
- **Consistent terminology** - Maintain consistent language across similar form fields
- **Action context** - Labels should indicate what users should do or provide
- **Business language** - Use terminology familiar to users in their business context

**Help text usage:**
- **Clarification** - Use help text to explain complex requirements or provide examples
- **Format guidance** - Explain expected formats, patterns, or constraints
- **Business context** - Provide context about why information is needed
- **Error prevention** - Help users avoid common mistakes through proactive guidance

**Required field handling:**
- **Visual indicators** - Required fields are clearly marked through design system patterns
- **Optional text** - Optional fields show "optional" text when `required={false}`
- **Consistent patterns** - Maintain consistent required/optional patterns across forms
- **Clear expectations** - Users should understand what's required before attempting submission

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships using label, fieldset, and legend elements
- Full keyboard navigation support with logical tab order and proper focus management
- Screen reader announcements for labels, help text, validation feedback, and field relationships
- High contrast support that works with system accessibility preferences and meets WCAG AA standards
- Scalable typography and spacing that works with browser zoom up to 200% magnification
- Proper ARIA relationships including `aria-describedby`, `aria-labelledby`, and `htmlFor` attributes

**Development responsibilities:**
- Provide meaningful `label` prop that clearly describes the field's purpose and requirements
- Use appropriate `role` prop (radiogroup, group) for grouped inputs like radio buttons and checkboxes
- Implement proper `renderInput` function that uses all provided accessibility props
- Ensure `formId` and `name` props create proper form associations for data submission
- Handle validation feedback appropriately with clear, actionable error messages
- Use `cosmosUseWithCaution_hideLabelFromScreenReaders` only when inputs provide their own accessible labels

**Design responsibilities:**
- Provide sufficient color contrast for all field states, labels, and feedback across different themes
- Design clear visual hierarchy that shows relationships between labels, inputs, help text, and feedback
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for required/optional fields, validation states, and field layouts
- Design appropriate spacing and sizing for different layout options (stack, input-left, input-right)
- Ensure feedback styling provides clear visual cues that complement screen reader announcements

