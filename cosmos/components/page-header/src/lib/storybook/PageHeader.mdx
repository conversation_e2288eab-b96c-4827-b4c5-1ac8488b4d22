import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as PageHeaderStories from './PageHeader.stories';

<Meta of={PageHeaderStories} />

<Title />

<Description />

<Primary />

<Controls of={PageHeaderStories.Playground} />

## Import

```jsx
import { PageHeader } from '@drata/cosmos-page-header';
```

## 🟢 When to use the component

- **Application pages** - Use at the top of every main application page to provide consistent page identification and navigation context
- **Page metadata communication** - Show critical metadata pertaining to the page
- **Page-level actions** - Highlight functions and actions that impact the page as a whole, such as creating new objects, managing connections, or key status changes
- **Parent-child workflows** - When users navigate from a parent object to related sub-objects and need to maintain context of the relationship

## ❌ When not to use the component

- **Non-top page locations** - Don't use to communicate page-level information anywhere other than the top of the page
- **Section headers within pages** - Use heading components or panel headers for content sections within a page
- **Modal or dialog titles** - Use ModalHeader component for modal dialog titles instead
- **Card or panel titles** - Use appropriate title props on Card or Panel components for contained content
- **Navigation-only elements** - Use dedicated navigation components when you only need navigation without page context

## 🛠️ How it works

The PageHeader component provides consistent page titles, navigation elements, and action controls at the top of application pages with support for breadcrumbs and loading states.

**Layout structure:**
- **Back link** - Optional navigation link to return to the previous page, useful for parent-child relationships
- **Breadcrumbs** - Hierarchical navigation showing the user's current location
- **Title section** - Main page title (rendered as h1) that displays the title or topmost heading within a page to establish clear page hierarchy and identity. Optional slot supports icons, key IDs, or images
- **Action stack** - Primary page actions organized in a responsive action stack that highlights functions and actions impacting the page as a whole, such as creating new objects, managing connections, or key status changes
- **Key-value pairs** - Metadata display limited to five pairs to communicate page metadata like status, description, subtitle, or other contextual information; additional metadata should be moved to page content
- **Banner area** - Space for page-level alerts, notifications, or contextual information
- **Bottom slot** - Additional content area below the main header structure

**Responsive behavior:**
- **Adaptive layout** - Automatically switches between default and stacked layouts based on container width (834px breakpoint)
- **Action organization** - Actions stack vertically on smaller screens while maintaining proper spacing
- **Content reflow** - Title, slot, and metadata reorganize appropriately for different screen sizes

**Technical features:**
- **Loading states** - Built-in skeleton loading with customizable skeleton support
- **Metadata detection** - Automatically detects Metadata components in the slot for proper alignment
- **Grid-based layout** - Uses CSS Grid for flexible and responsive content organization
- **Test integration** - Comprehensive data-id attributes for testing and debugging

### Usability

**Layout patterns:**
- **Clear hierarchy** - Page title should be the most prominent element, followed by actions and metadata
- **Logical grouping** - Related actions should be grouped together in the action stack with appropriate button hierarchy
- **Consistent placement** - Maintain consistent patterns for similar types of pages across the application
- **Progressive disclosure** - Use key-value pairs to surface important metadata without overwhelming the interface

**Action patterns:**
- **Primary focus** - Use primary buttons for the most important page actions like object creation or key status changes
- **Supporting actions** - Group secondary actions logically while maintaining visual hierarchy
- **Contextual relevance** - Ensure actions are relevant to the current page content and user permissions

**Responsive considerations:**
- **Mobile adaptation** - Actions stack vertically on smaller screens while maintaining usability
- **Content prioritization** - Most important information remains visible at all screen sizes
- **Touch targets** - Ensure adequate spacing for touch interactions on mobile devices

### Content

**Writing guidelines:**
- **Descriptive titles** - Use clear, specific titles that identify the page purpose and context (recommend not exceeding 50 characters, though longer values will wrap)
- **Consistent naming** - Follow established naming conventions across similar pages in the application

**Content organization:**
- **Essential metadata** - Display only critical information like status, dates, types, or key identifiers in key-value pairs
- **Supporting elements** - Use slots for icons, status indicators, key IDs, or images that enhance page identification
- **Actionable messaging** - Use banners for page-specific notifications with clear next steps when user response is required

**Content constraints:**
- **Five-pair limit** - Restrict key-value pairs to five maximum to prevent header complexity; move additional metadata to page content
- **Scannable layout** - Organize information for quick comprehension and decision-making
- **Context awareness** - Tailor action labels to the specific page context and user workflow

### Accessibility

**What the design system provides:**
- Semantic structure with page titles rendered as h1 elements to establish proper heading hierarchy
- Keyboard navigation with full keyboard accessibility for all interactive elements including actions and links
- Screen reader support with proper heading structure and landmark navigation for assistive technology
- Focus management with logical tab order through header elements and action controls
- Responsive design that maintains usability and accessibility across different screen sizes and zoom levels

**Development responsibilities:**
- Single h1 rule to ensure the PageHeader title is the only h1 element on the page to maintain semantic hierarchy and avoid screen reader confusion
- Action accessibility to verify all action buttons have appropriate labels and keyboard accessibility
- Link context to provide sufficient context for back links and breadcrumb navigation
- Loading states to ensure loading skeletons maintain semantic structure for screen readers
- Dynamic content handling to update title and content appropriately for assistive technology

**Design responsibilities:**
- Visual hierarchy with clear visual relationships between title, actions, and metadata elements
- Focus indicators to ensure all interactive elements have clear focus states that meet WCAG guidelines
- Contrast requirements to maintain sufficient contrast for all text and interactive elements
- Responsive behavior to design layouts that work effectively across different screen sizes and orientations
- Loading feedback to provide clear visual feedback during loading states that works for all users
