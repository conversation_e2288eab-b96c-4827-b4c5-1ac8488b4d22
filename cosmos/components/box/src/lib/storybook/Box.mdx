import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as BoxStories from './box.stories';

<Meta of={BoxStories} />

<Title />

<Description />

<Primary />

<Controls of={BoxStories.Playground} />

## Import

```jsx
import { Box } from '@cosmos/components/box';
```

## 🟢 When to use the component

- **Layout containers** - When you need a flexible container with design system spacing, borders, and background colors
- **Flex and grid items** - Best used as flex-items or grid-items within layout systems
- **Visual grouping** - When you need to visually group content with consistent backgrounds or borders
- **Static styling** - For elements that need consistent visual treatment without interactive states
- **Content sections** - When creating distinct content areas that need visual separation or emphasis

## ❌ When not to use the component

- **Interactive elements** - Use Button, Card, or other interactive components for clickable or hoverable elements
- **Complex layouts** - Use Stack, Grid, or specialized layout components for structured content organization
- **Form elements** - Use form-specific components for input fields, labels, and form containers
- **Navigation elements** - Use dedicated navigation components for menus, links, and navigation structures
- **Data display** - Use Table, List, or data-specific components for structured information display
- **Typography containers** - Use Text component or semantic HTML elements for text-only content

## 🛠️ How it works

The Box component is a flexible layout component that provides Cosmos design tokens for spacing, borders, and background colors, built on Radix UI Box with enhanced dimension prop support.

**Visual styling:**
- **Background colors** - Uses allowed design system tokens for consistent background styling
- **Border system** - Configurable border colors, widths, radius, and positioning (all sides, specific sides, or single sides)
- **Single border control** - Apply borders to individual sides using `borderPosition` prop (`top`, `bottom`, `left`, `right`, `x`, `y`, or `all`)
- **Design tokens** - All styling uses design system tokens for consistency and maintainability
- **Static styling** - Optimized for static elements without hover, active, or focus states

**Layout features:**
- **Dimension props** - Full support for width, height, padding, margin, and other spacing properties
- **Flexible container** - Works as both flex and grid items with appropriate sizing behavior
- **Responsive support** - Integrates with responsive design patterns through dimension props
- **CSS Grid integration** - Optimized for use within CSS Grid layouts

**Technical implementation:**
- **Radix UI foundation** - Built on Radix UI Box with additional Cosmos enhancements
- **Token validation** - Only allows approved design system tokens for consistent styling
- **Performance optimized** - Minimal overhead with efficient styled-components implementation
- **Accessibility ready** - Renders semantic HTML div elements with proper attributes

### Usability

**Layout patterns:**
- **Container hierarchy** - Use Box as a building block within larger layout systems
- **Visual separation** - Apply borders and backgrounds to create clear content boundaries
- **Spacing consistency** - Leverage dimension props for consistent spacing patterns
- **Responsive behavior** - Combine with responsive design patterns for adaptive layouts

**Styling approach:**
- **Token-based design** - Always use design system tokens rather than custom CSS values
- **Static elements** - Focus on non-interactive styling for consistent visual treatment
- **Border flexibility** - Use border positioning to create partial borders (top, bottom, left, right, or all)
- **Background purpose** - Apply backgrounds for visual grouping rather than decoration

### Content

**Content organization:**
- **Logical grouping** - Use Box to group related content elements together
- **Visual hierarchy** - Apply consistent styling to establish content importance
- **Spacing rhythm** - Use consistent padding and margin patterns across similar content areas
- **Border usage** - Apply borders to create clear content boundaries and visual separation

**Implementation patterns:**
- **Flex items** - Commonly used within Stack or other flex containers
- **Grid items** - Frequently used as grid items within Grid layouts
- **Content wrappers** - Wrap content that needs consistent visual treatment
- **Section containers** - Create distinct sections within larger page or component layouts

### Accessibility

**What the design system provides:**
- Semantic HTML structure using div elements that don't interfere with content semantics
- High contrast support that works with system preferences and design system color tokens
- Scalable design that maintains visual clarity at different zoom levels and screen sizes
- Consistent focus behavior that doesn't interfere with keyboard navigation of child content
- Screen reader compatibility that allows assistive technology to read content naturally

**Development responsibilities:**
- Content structure to ensure Box usage doesn't create confusing content hierarchy for screen readers
- Semantic markup to use appropriate semantic elements within Box containers when needed
- Color contrast to verify that custom background and border combinations meet accessibility requirements
- Keyboard navigation to ensure Box styling doesn't interfere with interactive child elements
- Responsive design to ensure Box layouts work effectively across different screen sizes and orientations

**Design responsibilities:**
- Visual hierarchy to ensure Box styling supports rather than hinders content understanding
- Color accessibility to maintain sufficient contrast ratios with design system tokens
- Layout clarity to ensure Box boundaries enhance rather than confuse content organization
- Consistent patterns to apply Box styling consistently across similar interface contexts

