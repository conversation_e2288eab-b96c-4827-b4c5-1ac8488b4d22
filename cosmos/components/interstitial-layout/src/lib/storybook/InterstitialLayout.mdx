import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as InterstitialLayoutStories from './InterstitialLayout.stories';

<Meta of={InterstitialLayoutStories} />

<Title />

<Description />

<Primary />

<Controls of={InterstitialLayoutStories.Playground} />

## Import

```jsx
import { InterstitialLayout } from '@drata/cosmos-interstitial-layout';
```

## 🟢 When to use the component

- **Authentication screens** - For login, registration, and password reset experiences that need focused user attention
- **Loading states** - When displaying full-screen loading experiences with background imagery and progress indicators
- **Onboarding flows** - For welcome screens, setup wizards, and initial user configuration experiences
- **Error pages** - For 404, 500, and other full-screen error states that need clear messaging and recovery actions
- **Marketing content** - When displaying promotional content or feature announcements with rich visual backgrounds
- **Terms and conditions** - For legal agreements, privacy policies, and other standalone content pages

## ❌ When not to use the component

- **Standard page layouts** - Use regular page layout components for typical application content with navigation
- **Modal dialogs** - Use Modal component for overlay content that doesn't require full-screen treatment
- **Form-heavy interfaces** - Use standard form layouts when forms are the primary content without special branding needs
- **Navigation-dependent content** - Avoid when users need access to primary navigation or multiple page sections

## 🛠️ How it works

The InterstitialLayout component provides a centered layout container with optional background imagery for loading states, authentication screens, and full-screen content.

**Layout options:**
- **Content alignment** - Left, center, or right alignment using the `alignment` prop (defaults to center)
- **Background imagery** - Optional background image support via `backgroundImage` prop
- **Page content** - Main content area provided through the `page` prop
- **Alternative content** - Secondary content with its own background image and copy via `alternativeContent` prop

**Visual structure:**
- **Full-screen container** - Takes up entire viewport with flexible layout
- **Content area** - Fixed max-width (600px) container for main content with neutral background
- **Alternative section** - Flexible area for supplementary content with custom background imagery
- **Responsive behavior** - Adapts to different screen sizes while maintaining content readability

**Technical implementation:**
- Uses CSS Flexbox for layout positioning and alignment
- Supports background image URLs with proper CSS background properties
- Integrates with design system color tokens for consistent styling
- Provides semantic HTML structure with appropriate test IDs

### Usability

**Layout patterns:**
- **Authentication flows** - Commonly used with left alignment for login forms with branded background content
- **Loading experiences** - Center alignment for loading spinners and progress indicators
- **Error recovery** - Center alignment for error messages with clear recovery actions

**Content organization:**
- **Primary content** - Main interactive content (forms, messages) goes in the `page` prop
- **Supporting content** - Branding, marketing copy, or contextual information goes in `alternativeContent`
- **Visual hierarchy** - Background imagery supports but doesn't compete with primary content

**User expectations:**
- Users expect focused, distraction-free experiences in interstitial layouts
- Clear visual separation between functional content and decorative elements
- Consistent branding and messaging that aligns with the application's identity
- Obvious next steps or actions to continue their journey

### Content

**Page content guidelines:**
- **Focused messaging** - Keep primary content concise and action-oriented
- **Clear hierarchy** - Use appropriate heading levels and visual emphasis
- **Minimal navigation** - Avoid complex navigation patterns that distract from the primary task
- **Responsive text** - Ensure content remains readable at all screen sizes

**Alternative content usage:**
- **Brand reinforcement** - Use for company messaging, value propositions, or feature highlights
- **Contextual information** - Provide relevant background information that supports the primary task
- **Visual interest** - Include imagery that enhances the experience without overwhelming the interface
- **Consistent messaging** - Maintain brand voice and tone across all content areas

**Background imagery:**
- **High quality visuals** - Use professional imagery that reflects brand standards
- **Appropriate contrast** - Ensure background images don't interfere with text readability
- **Relevant content** - Choose imagery that supports the page's purpose and user context
- **Performance considerations** - Optimize images for web delivery and various screen densities

### Accessibility

**What the design system provides:**
- Semantic HTML structure using appropriate div elements for layout organization
- Responsive design that adapts to different screen sizes and zoom levels
- High contrast support that works with system preferences and accessibility settings
- Proper text contrast against background images and colored surfaces
- Scalable layout that maintains usability across different viewport sizes

**Development responsibilities:**
- Ensure all interactive elements within the layout meet accessibility standards
- Implement proper focus management for any forms or interactive content
- Provide alternative text or descriptions for decorative background imagery
- Test layout functionality with keyboard navigation and screen readers
- Verify content remains accessible at different zoom levels and screen sizes
- Ensure color is not the only means of conveying important information

**Design responsibilities:**
- Design sufficient contrast ratios between text and background elements
- Ensure focus states are clearly visible against background imagery
- Create layouts that work well with high contrast and reduced motion settings
- Design clear visual hierarchy that works for users with cognitive disabilities
- Avoid relying solely on color or imagery to convey critical information

