import { Controls, Description, Meta, Primary, Title } from '@storybook/addon-docs/blocks';
import * as InputStories from './Input.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={InputStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />
<br />

## Content Guidelines

<Primary />

<Controls of={InputStories.Playground} />

## Import

```jsx
import { Input } from '@drata/cosmos-input';
```

## 🟢 When to use the component

- **Building Cosmos form components** - Use as the foundational input element when creating new form field components for the design system
- **Custom form field composition** - Combine with FormField, FieldLabel, and FieldFeedback to build complete form field components
- **Low-level input control** - When you need direct control over input behavior while maintaining design system consistency

## ❌ When not to use the component

- **Application development** - Use complete form field components (TextField, NumberField, etc.) instead of building with Input directly
- **Standard text input needs** - Use TextField which provides complete form field functionality
- **Complex input patterns** - Use specialized components like DateField, SelectField, or ComboboxField for specific input types
- **Multi-line text** - Use TextareaField for multi-line text input requirements
- **File uploads** - Use FileUploadField or ImageUploadField for file input functionality

## 🛠️ How it works

The Input component provides foundational input functionality for building other Cosmos form components, handling controlled and uncontrolled states, validation feedback styling, and accessibility features.

**Component structure:**
- **Forwardable ref** - Uses `forwardRef` to allow parent components to access the underlying input element
- **Styled wrapper** - Uses `StyledInput` for consistent design system styling and theming
- **State management** - Handles both controlled (`value` prop) and uncontrolled (`defaultValue` prop) input patterns
- **Feedback integration** - Accepts `feedbackType` for validation state styling coordination

**Input state handling:**
- **Controlled mode** - When `value` prop is provided, component is fully controlled by parent
- **Uncontrolled mode** - When only `defaultValue` is provided, component manages its own internal state
- **State detection** - Automatically determines mode based on presence of `value` prop
- **Change handling** - `onChange` callback works in both controlled and uncontrolled modes
- **Read-only support** - `readOnly` prop prevents user input while maintaining visual state

**Validation feedback:**
- **Visual feedback** - `feedbackType` prop applies appropriate border colors for validation states
- **Error styling** - Red border color for error feedback type
- **Success styling** - Green border color for success feedback type (if implemented)
- **Neutral state** - Default styling when no feedback type is provided
- **ARIA integration** - Accepts standard ARIA props for accessibility relationships

**Layout integration:**
- **CSS Grid support** - `gridArea` prop enables positioning within CSS Grid layouts
- **Flexible sizing** - Inherits sizing from parent container or CSS Grid area
- **Responsive behavior** - Adapts to container constraints and responsive design patterns

**Accessibility features:**
- **Standard HTML input** - Maintains all native input accessibility features
- **ARIA support** - Accepts all standard ARIA attributes for form field relationships
- **Read-only handling** - Uses `aria-disabled` for read-only state accessibility
- **Focus management** - Proper focus behavior and keyboard navigation support

### Usability

**State management patterns:**
- **Controlled usage** - Parent components manage value state and pass to Input via `value` prop
- **Uncontrolled usage** - Input manages its own state when only `defaultValue` is provided
- **Read-only display** - Use `readOnly` prop for displaying values that shouldn't be edited
- **Change detection** - `onChange` callback provides access to input changes in both modes

**Integration with form systems:**
- **FormField composition** - Designed to work within FormField wrapper for complete form field functionality
- **Validation coordination** - `feedbackType` prop coordinates with validation systems for visual feedback
- **Grid positioning** - `gridArea` prop enables precise positioning within form layouts
- **Ref forwarding** - Parent components can access input element for focus management or validation

**Input behavior:**
- **Standard HTML input** - Maintains familiar input behavior and keyboard interactions
- **Placeholder support** - Standard `placeholder` attribute for user guidance
- **Type variations** - Supports all HTML input types (text, email, password, number, etc.)
- **Attribute passthrough** - All standard HTML input attributes are supported

### Content

**Input configuration:**
- **Appropriate types** - Use correct `type` attribute for input purpose (email, password, tel, etc.)
- **Meaningful placeholders** - Use placeholder text that provides helpful examples or guidance
- **Default values** - Provide sensible default values when appropriate for user experience
- **Value formatting** - Handle value formatting in parent components, not within Input itself

**Accessibility considerations:**
- **Label relationships** - Ensure proper label association through parent FormField component
- **ARIA attributes** - Use appropriate ARIA attributes for complex form field relationships
- **Error associations** - Connect validation feedback through proper ARIA relationships
- **Required field handling** - Use standard `required` attribute and ARIA for required fields

**Integration patterns:**
- **Consistent styling** - Rely on design system styling rather than custom CSS
- **Validation feedback** - Use `feedbackType` prop to coordinate with validation systems
- **Form field composition** - Always use within FormField or similar wrapper for complete functionality
- **State management** - Choose controlled vs uncontrolled based on form architecture needs

### Accessibility

**What the design system provides:**
- Standard HTML input element with full native accessibility support including keyboard navigation and screen reader compatibility
- High contrast support that works with system accessibility preferences and meets WCAG AA standards for all input states
- Scalable typography and spacing that works with browser zoom up to 200% magnification
- Proper focus indicators that are clearly visible and meet contrast requirements
- Read-only state handling with appropriate `aria-disabled` attribute for assistive technology
- Consistent styling across all input states that maintains accessibility across different themes

**Development responsibilities:**
- Ensure proper label association through parent FormField component using `htmlFor` and `id` attributes
- Implement appropriate ARIA relationships including `aria-describedby` for help text and validation feedback
- Use correct input `type` attribute for the data being collected (email, tel, password, etc.)
- Provide meaningful placeholder text that doesn't replace proper labeling
- Handle validation states appropriately with clear error messaging through parent components
- Ensure `onChange` handlers work correctly in both controlled and uncontrolled modes

**Design responsibilities:**
- Provide sufficient color contrast for all input states including default, focus, error, and read-only across different themes
- Design clear visual hierarchy that shows input state and validation feedback
- Ensure focus indicators are clearly visible and meet contrast requirements for all input variations
- Create consistent visual patterns for input styling across all form components in the design system
- Design appropriate sizing and spacing that works across different screen sizes and form layouts
- Ensure validation feedback styling provides clear visual cues that complement screen reader announcements

