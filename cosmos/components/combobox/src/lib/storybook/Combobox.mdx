import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';

import * as ComboboxStories from './Combobox.stories';

<Meta of={ComboboxStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for ComboboxField."
/>
<br />

<Description />

<Primary />

<Controls of={ComboboxStories.Playground} />

## Import

```jsx
import { Combobox } from '@cosmos/components/combobox';
```

## 🟢 When to use the component

- **Building form components** - As the foundational component for creating ComboboxField and other select-based form controls
- **Custom dropdown implementations** - When you need full control over dropdown behavior and styling within the design system
- **Advanced search scenarios** - For implementing complex search and filtering functionality with custom logic
- **Multi-select interfaces** - When building components that need sophisticated multi-selection with tag display
- **Async data loading** - For components that need to fetch options dynamically with search debouncing and pagination

## ❌ When not to use the component

- **Standard form fields** - Use ComboboxField for typical form implementations
- **Simple dropdowns** - Use SelectField for basic single-selection dropdowns without search
- **Navigation menus** - Use Dropdown or NavigationMenu components for action-oriented menus
- **Direct application use** - This is a foundational component; use higher-level form components instead

## 🛠️ How it works

The Combobox component provides foundational select functionality with search capabilities, supporting both single and multi-select modes with advanced features like async loading and custom filtering.

**Core functionality:**
- **Search integration** - Built-in input field with configurable debouncing for search queries
- **Selection modes** - Supports both single-select and multi-select with different state management patterns
- **Async data loading** - Optional `onFetchOptions` with support for pagination and search-based filtering
- **Keyboard navigation** - Full keyboard support including arrow keys, Enter, Escape, and type-ahead

**Component architecture:**
- **ComboboxTrigger** - Input field with clear button, loading states, and selected item display
- **Popover integration** - Uses Popover component for dropdown positioning with collision detection
- **ListBox rendering** - Renders options using ListBox component with proper ARIA roles and selection states
- **TagGroup display** - Multi-select mode shows selected items as removable tags below the input

**State management:**
- **Single-select state** - Uses `defaultValue` and `onChange` with single item or undefined
- **Multi-select state** - Manages internal `selectedItems` array with `defaultSelectedOptions` and array-based `onChange`
- **Input value tracking** - Separate input value state for search queries independent of selection

### Usability

**Search behavior:**
- **Immediate feedback** - Input changes trigger search with appropriate debouncing
- **Clear search state** - Search input remains independent of selection for continued filtering
- **Loading indicators** - Clear visual feedback during async operations
- **Empty state handling** - Meaningful messages when no results match search criteria

**Selection patterns:**
- **Single-select** - Clear selection with optional clear button, immediate onChange callback
- **Multi-select** - Tag-based display with individual remove buttons and clear-all functionality
- **Keyboard efficiency** - Full keyboard navigation without requiring mouse interaction
- **Focus management** - Proper focus handling between input, options, and selected tags

**Performance considerations:**
- **Debounced requests** - Prevents excessive API calls during typing
- **Efficient re-renders** - Optimized state updates to minimize unnecessary component re-renders
- **Memory management** - Proper cleanup of debounced functions and event listeners

### Content

**Option organization:**
- **Meaningful labels** - Use clear, descriptive labels that help users identify options
- **Consistent structure** - Maintain consistent option data structure across similar implementations
- **Logical grouping** - When using grouped options, organize in user-friendly categories
- **Search optimization** - Structure labels and searchable content for effective filtering

**Placeholder and labels:**
- **Descriptive placeholders** - Use `placeholderText` that guides users on what to search or select
- **Clear instructions** - Provide context about search behavior and selection expectations
- **Loading feedback** - Use meaningful `loaderLabel` text for accessibility during async operations
- **Action labels** - Provide clear labels for clear buttons and remove actions in multi-select mode

**Multi-select content:**
- **Tag display** - Selected items appear as tags with clear remove functionality
- **Bulk actions** - Clear "remove all" labeling when `removeAllSelectedItemsLabel` is provided
- **Individual removal** - Descriptive labels for individual tag removal via `getRemoveIndividualSelectedItemClickLabel`
- **Color coding** - Appropriate `tagGroupColorScheme` selection for visual hierarchy

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper ARIA roles (combobox, listbox, option) and relationships
- Full keyboard navigation including arrow keys, Enter/Space selection, Escape to close, and Tab navigation
- Screen reader announcements for selection changes, search results, loading states, and multi-select operations
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators and logical focus flow between input, options, and tags
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile interaction

**Development responsibilities:**
- Provide unique, stable `id` prop for proper ARIA relationships and form association
- Ensure `loaderLabel` is descriptive for screen reader users during loading states
- Use meaningful `aria-labelledby` and `aria-describedby` props to connect with form labels and help text
- Implement proper error handling with accessible feedback for failed search or selection operations
- Ensure `itemToString` function returns meaningful text for screen reader announcements
- Provide descriptive labels for all interactive elements (clear buttons, remove tags, etc.)

**Design responsibilities:**
- Provide sufficient color contrast for all states including focus, selection, disabled, and loading across themes
- Design clear visual hierarchy that distinguishes between input, dropdown, selected items, and interactive elements
- Ensure focus indicators are clearly visible and meet contrast requirements for all focusable elements
- Create consistent visual patterns for similar combobox implementations across the application
- Design appropriate spacing and sizing for touch targets, dropdown positioning, and tag display across screen sizes
- Ensure loading states and empty states provide clear visual feedback that complements screen reader announcements

