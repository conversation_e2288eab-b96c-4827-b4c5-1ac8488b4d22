import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as GridStories from './Grid.stories';

<Meta of={GridStories} />

<Title />

<Description />

<Primary />

<Controls of={GridStories.Playground} />

## Import

```jsx
import { Grid } from '@drata/cosmos-grid';
```

## Variants

### Column Width

<Canvas of={GridStories.ColumnWidth} />

### Column Count

<Canvas of={GridStories.ColumnCount} />

## 🟢 When to use the component

- **Responsive layouts** - When you need flexible grid systems that adapt to different screen sizes with responsive column counts
- **Card collections** - For displaying multiple cards, gallery items, or similar content in organized rows and columns
- **Dashboard layouts** - When building complex dashboard interfaces with multiple content sections that need structured positioning
- **Form layouts** - For organizing form fields in multi-column layouts that adapt to screen size
- **Content galleries** - When displaying images, media, or other visual content in grid formations
- **Data visualization layouts** - For arranging charts, metrics, or other data components in structured grids

## ❌ When not to use the component

- **Simple linear layouts** - Use Stack component for straightforward vertical or horizontal arrangements
- **Single column content** - Use Stack or basic div containers when content naturally flows in one column
- **Complex nested layouts** - Consider specialized layout components when grids become overly complex with deep nesting
- **Table-like data** - Use Datatable component for structured data that needs sorting, filtering, or column-based operations
- **Navigation layouts** - Use dedicated navigation components rather than grids for menu structures

## 🛠️ How it works

The Grid component provides flexible CSS grid layouts with responsive columns and gaps for organizing content using Radix UI Grid as the foundation.

**Column configuration:**
- **Column count** - Set specific number of columns using `columns` prop (e.g., `columns="3"` or `columns={3}`)
- **Responsive columns** - Use responsive objects for different screen sizes (e.g., `columns={{ initial: '1', sm: '2', lg: '3' }}`)
- **Column width control** - Define minimum and maximum column widths with `columnWidth` prop
- **Auto-fit/fill behavior** - Control how columns adapt to available space

**Layout features:**
- **Flexible gaps** - Configurable spacing between grid items using design system tokens
- **Responsive behavior** - Automatic adaptation to different screen sizes and container widths
- **Dimension props integration** - Supports all standard dimension props for width, height, padding, and margin

**Technical implementation:**
- Built on Radix UI Grid with enhanced dimension prop support
- Uses CSS Grid under the hood for optimal performance and browser compatibility
- Integrates with design system tokens for consistent spacing and sizing
- Supports custom data-id attributes for testing and debugging

### Usability

**Layout patterns:**
- **Dashboard grids** - Use consistent column counts and gaps for dashboard card layouts
- **Gallery displays** - Implement responsive column counts that adapt from mobile to desktop
- **Form organization** - Structure form fields in logical groups with appropriate column spans

**Responsive considerations:**
- **Mobile-first approach** - Start with single column layouts and progressively enhance for larger screens
- **Breakpoint strategy** - Use design system breakpoints for consistent responsive behavior across the application
- **Content adaptation** - Ensure grid items work well at different sizes and aspect ratios

**Performance optimization:**
- **Efficient rendering** - Grid uses CSS Grid for optimal browser performance
- **Minimal re-renders** - Component optimized to minimize unnecessary re-calculations
- **Scalable layouts** - Handles large numbers of grid items efficiently

### Content

**Grid item guidelines:**
- **Consistent sizing** - Ensure grid items have similar content density and visual weight
- **Flexible content** - Design grid items to work well at different sizes and aspect ratios
- **Content hierarchy** - Use consistent patterns for titles, metadata, and actions across grid items

**Spacing and alignment:**
- **Consistent gaps** - Use design system spacing tokens for predictable and harmonious layouts
- **Visual rhythm** - Maintain consistent spacing patterns across different grid implementations
- **Content padding** - Ensure grid items have appropriate internal spacing that works with grid gaps

**Responsive content strategy:**
- **Content prioritization** - Show most important content first on smaller screens
- **Progressive disclosure** - Reveal additional content as screen size increases
- **Adaptive layouts** - Adjust content layout within grid items based on available space

### Accessibility

**What the design system provides:**
- Semantic HTML structure using appropriate div elements with CSS Grid for layout without interfering with content semantics
- Responsive design support that automatically adapts to different screen sizes and zoom levels
- High contrast compatibility that works with system high contrast modes and user preferences
- Keyboard navigation flow that maintains logical tab order through grid items
- Screen reader compatibility that doesn't interfere with assistive technology reading flow

**Development responsibilities:**
- Ensure grid items are ordered logically in the DOM for screen readers
- Implement proper focus handling for interactive grid items
- Verify grid layouts work well at different zoom levels and screen sizes
- Ensure individual grid items meet accessibility standards
- Test keyboard navigation through grid layouts with assistive technology

**Design responsibilities:**
- Design clear visual relationships between grid items and their content
- Ensure adequate spacing between grid items for visual clarity
- Design grid layouts that work well across all target screen sizes
- Maintain consistent patterns for similar types of grid content
- Design clear focus states for interactive grid items that meet WCAG guidelines

