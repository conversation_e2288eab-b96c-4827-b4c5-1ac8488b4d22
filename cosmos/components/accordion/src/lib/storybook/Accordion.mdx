import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as AccordionStories from './Accordion.stories';

<Meta of={AccordionStories} />

<Title />

<Description />

<Primary />

<Controls of={AccordionStories.Playground} />

## Import

```jsx
import { Accordion } from '@drata/cosmos-accordion';
```

## Props

### `titleType`

The `titleType` prop allows you to specify icon & title size.

1. `md` standard size
2. `lg` oversized size

<Canvas of={AccordionStories.TitleType} />

## Using Multiple Accordions for Data Display

Multiple Accordion components work together as a powerful data display pattern for collapsible grouped content. This pattern is ideal for presenting complex information that users can explore progressively.

<Canvas of={AccordionStories.DataDisplayList} />

This example demonstrates how multiple Accordions can be used to display policies, controls, or documentation sections where each item contains detailed information that users can expand as needed.

### Implementation Example

```jsx
import { Accordion } from '@drata/cosmos-accordion';
import { Stack } from '@cosmos/components/stack';

// Multiple Accordions for data display
<Stack direction="column" gap="md">
    <Accordion
        title="Access Control Policy"
        titleType="lg"
        iconSlot={{
            slotType: 'metadata',
            typeProps: {
                type: 'status',
                label: 'AC-001',
                iconName: 'CheckCircle',
                colorScheme: 'success',
            },
        }}
        body={/* Detailed policy content */}
    />

    <Accordion
        title="Data Classification and Handling"
        titleType="lg"
        iconSlot={{
            slotType: 'metadata',
            typeProps: {
                type: 'status',
                label: 'DC-002',
                iconName: 'Warning',
                colorScheme: 'warning',
            },
        }}
        body={/* Detailed classification content */}
    />

    {/* Additional Accordions... */}
</Stack>
```

## Nesting

Nesting is allowed and interactive elements can be used within Accordion content.

> A faded background can be used to "elevate" the nested accordion.

<Canvas of={AccordionStories.Nesting} />

## 🟢 When to use the component

Use Accordion when you need to present information with progressive disclosure. It's ideal for:

**Single Accordion:**
- **Collapsible sections** within forms, cards, or detailed views
- **Optional content** that doesn't need to be visible by default
- **Nested information** that provides additional context when needed

**Multiple Accordions (Data Display Pattern):**
- **~3–10 expandable items** with varying content lengths
- **Lists where each item contains additional, toggleable details** using multiple Accordion components
- **Grouping related content** in a vertical stack with progressive disclosure
- **Collapsing dense information** to keep pages clean and scannable
- **Perfect for Controls, policies, documentation sections, or any Drata object** with detailed information
- **When screen real estate is limited** but detailed information must be accessible

#### Perfect for:
- Policy and procedure documentation with expandable details
- Control frameworks with detailed implementation guidance
- Settings panels with advanced configuration options
- Help documentation with expandable sections
- Complex forms with optional or conditional fields

## ❌ When not to use the component

**For Single Items:**
- **Content should always be visible** - Use standard layout instead
- **Information is brief and doesn't need collapsing** - Use simple text or cards
- **Users need to see all content simultaneously** - For comparison or workflow

**For Multiple Items (Data Display):**
- **Flat, short items without additional details** - Use [`StackedList`](?path=/docs/information-data-stackedlist--docs)
- **Large datasets that require bulk operations** - Use [`Datatable`](?path=/docs/information-data-datatable--docs)
- **Simple labeled facts or metadata** - Use [`KeyValuePair`](?path=/docs/information-data-keyvaluepair--docs)
- **Content where users typically need to see all details simultaneously** for comparison
- **More than 10 items** - Consider pagination or filtering with other components

## 🛠️ How it works

### Usability

Accordion follows progressive disclosure principles designed for optimal information consumption:

**Single Accordion Usage:**
- **Clear expand/collapse states** with visual indicators for current state
- **Smooth animations** that provide feedback during state transitions
- **Flexible content support** for text, interactive elements, and nested components
- **Icon slot integration** for visual context and status indicators

**Multiple Accordions (Data Display Pattern):**
- **Independent operation** - each Accordion can be expanded/collapsed separately
- **Consistent visual hierarchy** with uniform spacing and styling
- **Status indication** through icon slots showing completion, warnings, or other states
- **Scannable layout** allowing users to quickly identify items of interest

**Best Practices for Data Display:**
- **Use consistent spacing** between multiple Accordions (typically `md` gap)
- **Provide status indicators** through icon slots when showing progress or completion
- **Keep titles descriptive** and scannable for quick identification
- **Group related Accordions** together with appropriate container components

### Design Principles

**Progressive disclosure** - Essential information visible, details available on demand with consistent expand/collapse behavior across all instances. Titles should be prominent enough to scan quickly but not overpower the overall layout, with clear visual distinction between collapsed and expanded states.

**Flexible content support** - Supports various content types while maintaining visual consistency. Content automatically adapts to available space while maintaining readability, with touch targets remaining accessible across different screen sizes.

**Visual hierarchy and organization** - Group related Accordions together with appropriate spacing and container structure. Use consistent title patterns across similar types of content (e.g., all policies, all controls) and provide meaningful status indicators through icon slots that help users understand completion, priority, or state.

**Best practices for multiple Accordions** - Limit to ~3–10 items for optimal user experience with consistent spacing (typically `md` gap) between items. Consider the user's workflow and whether they need multiple items open simultaneously. Provide clear visual feedback for loading states when content is dynamic and group related Accordions under descriptive headings for better organization.


### Content

**Required Content:**
- `title`: The header text that identifies the accordion section (required)
- `body`: The collapsible content that provides detailed information (required)

**Optional Content:**
- `titleType`: Controls the size and visual weight of the title (`md` or `lg`)
- `iconSlot`: Provides visual context through icons or metadata badges
- `supportingContent`: Additional information displayed alongside the title
- `isDefaultExpanded`: Controls initial state for better user experience
- `isBodyFaded`: Adds visual distinction for nested or secondary content

**Content Guidelines for Data Display:**
- **Keep titles concise and descriptive** for easy scanning
- **Use consistent title patterns** across related Accordions
- **Provide meaningful status indicators** through icon slots
- **Structure body content** with clear hierarchy and actionable elements
- **Include relevant actions** within the body content when appropriate
- **Consider content length** - aim for focused, digestible information per section

### Accessibility

**What the design system provides:**
- Semantic button structure with built-in `button` element and proper ARIA attributes (`aria-expanded`, `aria-controls`)
- Full keyboard accessibility with Enter and Space key activation
- Clear visual focus indicators and logical tab order
- Proper announcement of expanded/collapsed states and content relationships for screen readers
- Smooth transitions that respect `prefers-reduced-motion` settings
- Interactive elements meet accessibility guidelines with minimum 44px touch targets
- Design system tokens ensure WCAG 2.1 AA contrast compliance

**Development responsibilities:**
- Ensure titles clearly describe the content within each Accordion
- Use appropriate heading hierarchy within Accordion body content
- Verify expand/collapse behavior works correctly with screen readers
- Ensure proper announcements when Accordion content changes
- When using multiple Accordions, wrap in appropriate container with descriptive heading
- Ensure all interactive elements within Accordion body are keyboard accessible
- Consider focus management when Accordions contain forms or complex interactions
- Implement accessible feedback when Accordion content loads dynamically
- Ensure validation errors within Accordions are properly announced

**Design responsibilities:**
- Create clear visual hierarchy with sufficient contrast and visual distinction between titles and content
- Use icons, text, and other visual cues alongside color for status indication rather than relying on color alone
- Maintain adequate spacing between multiple Accordions for readability and scannable layouts
- Design clear visual states for collapsed, expanded, hover, focus, and disabled states
- Structure information logically within each Accordion section with proper content hierarchy
- Ensure touch targets and content remain accessible across different screen sizes and orientations
- Design meaningful icons that reinforce rather than replace textual information in icon slots
- Group related Accordions with consistent spacing (typically `md` gap) and container structure
- Use consistent title patterns across similar content types (e.g., all policies, all controls)
- Provide meaningful status indicators that help users understand completion, priority, or state
- Consider responsive behavior for complex body content and nested interactive elements on smaller screens

## Related Components

**For different data display needs:**
- **[`KeyValuePair`](?path=/docs/information-data-keyvaluepair--docs)** - For simple labeled facts and metadata (1-10 pairs)
- **[`StackedList`](?path=/docs/information-data-stackedlist--docs)** - For flat lists of similar items with consistent structure and optional actions
- **[`Datatable`](?path=/docs/information-data-datatable--docs)** - For structured grid data with sorting, filtering, and bulk operations

**For content composition:**
- **[Stack](?path=/docs/components-stack--docs)** - For organizing multiple Accordions with consistent spacing
- **[ActionStack](?path=/docs/components-actionstack--docs)** - For organizing actions within Accordion body content
- **[Metadata](?path=/docs/components-metadata--docs)** - For status indicators and badges in icon slots
- **[Text](?path=/docs/components-text--docs)** - For content formatting and hierarchy within Accordion body
