import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as CardStories from './Card.stories';

<Meta of={CardStories} />

<Title />

<Description />

<Primary />

<Controls of={CardStories.Playground} />

## Import

```jsx
import { Card } from '@drata/cosmos-card';
```

## Examples

### Cards filling vertical space with CSS Grid

<Canvas of={CardStories.FillAvailableContainerSpace} />

## Props

### `actions`

<Canvas of={CardStories.WithActions} />

### Loading State

The Card component supports a loading state that displays a skeleton UI while content is being fetched.

<Canvas of={CardStories.Loading} />

<Controls of={CardStories.Loading} include={['isLoading']} />

## 🟢 When to use the component

- **Grouped content display** - When you need to present related information together with a clear title and optional body content
- **Content with actions** - When displaying information that requires user interaction through action buttons or clickable behavior
- **Loading states** - When content needs to show skeleton loading while data is being fetched
- **Edit mode interfaces** - When building interfaces where users can modify card content with visual feedback
- **AI-powered content** - When displaying AI-generated content that needs special visual treatment with gradient borders
- **Detail page sections** - Use large size (`lg`) for primary content sections on detail pages to provide appropriate visual hierarchy and content prominence

## ❌ When not to use the component

- **Single text elements** - Use Text or other typography components for standalone text without grouping needs
- **Navigation-only content** - Use Link, Button, or navigation-specific components when the primary purpose is navigation
- **Simple data display** - Use simpler components like KeyValuePair or plain text when cards add unnecessary visual weight
- **Dense information layouts** - Use tables or lists when you need to display many items compactly without individual containers
- **Complex interactive content** - Consider specialized components when cards become too complex with many nested interactions

## 🛠️ How it works

The Card component provides a flexible container for grouped content with support for multiple interaction patterns and visual states.

**Container variants:**
- **Static cards** - Display content without interaction (default behavior)
- **Clickable cards** - Entire card becomes interactive when `onClick` is provided
- **Action-based cards** - Include specific action buttons while keeping the card container non-interactive

**Visual states:**
- **Raised appearance** - Adds box shadow when `isRaised={true}` (Should not used raised cards anymore in Constellation)
- **Edit mode** - Changes background color and shows actions inline when `isEditMode={true}`
- **Loading state** - Displays skeleton UI when `isLoading={true}` with optional custom skeleton
- **AI styling** - Applies gradient borders and removes standard borders when `colorScheme="ai"`

**Technical behavior:**
- Renders as `<button>` element when clickable, `<section>` element otherwise
- AI cards automatically disable raised styling regardless of `isRaised` prop
- Actions are limited to maximum number defined in component constants
- Error handling with console logging for onClick failures

### Usability

**Interaction patterns:**
- **Clickable cards** - Entire card surface is interactive with clear hover and focus states
- **Action buttons** - Specific actions are contained within the card but don't make the entire card clickable
- **Mixed interaction** - Edit mode allows both card-level and action-level interactions

**Layout considerations:**
- Cards expand to fill available container space by default
- Use CSS Grid or Flexbox containers for responsive card layouts
- Consider card density and spacing in multi-card layouts
- Maintain consistent card heights in grid layouts when possible

**User expectations:**
- Users expect clickable cards to have consistent hover and focus feedback
- Action buttons should have clear, descriptive labels
- Loading states should maintain card structure and dimensions
- Edit mode should provide clear visual feedback about interactive state

### Content

**Title requirements:**
- Always provide a descriptive title that clearly identifies the card's content or purpose
- Keep titles concise but informative
- Use sentence case for consistency with other UI elements

**Body content guidelines:**
- Body content is optional but should add meaningful information when present
- Structure body content with appropriate spacing and typography
- Consider content hierarchy within the card body
- Use consistent content patterns across similar cards

**Action button usage:**
- Limit actions to essential functions to avoid overwhelming users
- Use clear, action-oriented button labels (e.g., "View details", "Edit")
- Order actions by importance with primary actions first
- Consider action button size relative to card size

### Accessibility

**What the design system provides:**
- Semantic HTML structure using appropriate elements (`<button>` for clickable cards, `<section>` for static cards)
- Keyboard navigation support with proper focus management
- Screen reader compatibility with proper heading structure and content organization
- High contrast support that works with system preferences
- Touch target sizing that meets accessibility guidelines for interactive elements
- Proper focus indicators for clickable cards and action buttons
- ARIA attributes automatically applied based on interaction patterns
- Error handling that doesn't break assistive technology functionality
- Loading states that maintain semantic structure for screen readers

**Development responsibilities:**
- Ensure card titles provide meaningful context for screen reader users
- Associate action buttons with clear, descriptive labels
- Implement proper error messaging that's accessible to all users
- Test keyboard navigation flow through cards and their actions
- Verify that loading states communicate progress appropriately to assistive technology
- Write descriptive titles that work well when read by screen readers
- Structure body content with proper heading hierarchy if needed
- Ensure action button labels clearly describe their function
- Provide alternative text or descriptions for any visual-only content within cards

**Design responsibilities:**
- Design clear, visible focus states that meet WCAG guidelines
- Ensure sufficient contrast ratios for all text and interactive elements
- Structure content to work well with screen readers
- Design accessible error messaging that's clearly associated with the component
- Don't rely solely on color to convey interactive states or information

