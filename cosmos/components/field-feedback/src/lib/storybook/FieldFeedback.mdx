import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as FieldFeedbackStories from './FieldFeedback.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={FieldFeedbackStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />

<Primary />

<Controls of={FieldFeedbackStories.Playground} />

## Import

```jsx
import { FieldFeedback } from '@drata/cosmos-field-feedback';
```

## 🟢 When to use the component

- **Building form field components** - Use as the feedback foundation when creating new Cosmos form components
- **Form field composition** - Integrate with other internal components like FieldLabel and FormField to build complete form fields
- **Validation feedback** - Provide success or error messaging for form field validation states
- **Accessibility compliance** - Ensure proper ARIA relationships between form inputs and their feedback messages
- **Design system consistency** - Maintain consistent feedback styling and behavior across all form components

## ❌ When not to use the component

- **Standard form fields** - Use complete form field components that already include feedback functionality
- **General messaging** - Use Feedback component for non-form-related success or error messages
- **Page-level notifications** - Use Banner component for system-wide or page-level messaging
- **Interactive feedback** - Use components with built-in actions when users need to respond to feedback

## 🛠️ How it works

The FieldFeedback component provides foundational feedback messaging for building other Cosmos form components, displaying success or error states with icons and text in a consistent, accessible manner.

**Component structure:**
- **Icon integration** - Displays contextual icons (CheckCircle for success, WarningDiamond for error)
- **Text messaging** - Uses Text component for consistent typography and accessibility
- **Color coordination** - Applies appropriate colors based on feedback type (success green, error red)
- **Grid layout support** - Optional `gridArea` prop for CSS Grid positioning within form layouts

**Feedback types:**
- **Success feedback** - Green CheckCircle icon with success text color for positive validation states
- **Error feedback** - Red WarningDiamond icon with error text color for validation failures
- **Type-driven styling** - Colors and icons automatically determined by `type` prop
- **Consistent theming** - Uses design tokens for colors ensuring theme compatibility

**Accessibility features:**
- **Semantic structure** - Proper HTML structure with meaningful text content
- **ARIA integration** - Accepts `id` prop for ARIA relationships with form inputs
- **Screen reader support** - Icon uses `colorScheme="inherit"` and text is properly structured
- **Color independence** - Icons provide visual cues beyond color alone

### Usability

**Integration patterns:**
- **Form field composition** - Designed to be used within FormField and other form components
- **Validation timing** - Should appear immediately after validation occurs
- **Message persistence** - Feedback should remain visible until validation state changes
- **Visual hierarchy** - Positioned to clearly associate with the related form input

**Feedback presentation:**
- **Clear messaging** - Messages should be concise and actionable
- **Contextual icons** - Icons reinforce the message type without relying solely on color
- **Consistent positioning** - Maintains predictable placement across different form fields
- **Responsive behavior** - Adapts appropriately to different screen sizes and form layouts

**State management:**
- **Controlled display** - Parent components control when feedback appears and disappears
- **Type switching** - Can switch between success and error types based on validation state
- **Message updates** - Content can be updated dynamically as validation changes
- **Grid integration** - Can be positioned within CSS Grid layouts using `gridArea` prop

### Content

**Message guidelines:**
- **Specific feedback** - Messages should clearly explain what happened or what needs to be fixed
- **Actionable language** - Error messages should guide users toward resolution
- **Positive reinforcement** - Success messages should confirm what was accomplished
- **Consistent tone** - Maintain consistent voice and terminology across similar feedback

**Success messages:**
- **Confirmation clarity** - Clearly state what was successful (e.g., "Email address verified")
- **Brief and positive** - Keep messages concise while being encouraging
- **Action completion** - Confirm that the user's action was processed successfully
- **Next steps** - When appropriate, indicate what happens next

**Error messages:**
- **Problem identification** - Clearly explain what went wrong
- **Solution guidance** - Provide specific steps to resolve the issue
- **Avoid technical jargon** - Use language that users can understand and act upon
- **Helpful context** - Explain requirements or constraints when relevant

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper text content and icon integration for screen reader support
- High contrast color schemes that meet WCAG AA standards for both success and error states
- Scalable text and icons that work with browser zoom up to 200% magnification
- Color-independent visual cues through icon shapes that don't rely solely on color for meaning
- Proper text sizing and spacing for readability across different devices and accessibility settings

**Development responsibilities:**
- Provide unique, stable `id` prop for proper ARIA relationships with form inputs using `aria-describedby`
- Ensure feedback messages are clear, specific, and actionable for all users including screen reader users
- Connect feedback to form inputs using proper ARIA attributes in parent form components
- Ensure feedback appears and disappears at appropriate times in the validation flow
- Use meaningful `data-id` props for testing and debugging without affecting accessibility
- Coordinate with FormField and other form components to maintain proper ARIA relationships

**Design responsibilities:**
- Provide sufficient color contrast for both success and error states across all supported themes
- Design clear visual hierarchy that shows the relationship between form inputs and their feedback
- Ensure icons and text work together to convey meaning without relying solely on color
- Create consistent visual patterns for feedback across all form components in the design system
- Design appropriate spacing and sizing that works across different screen sizes and form layouts
- Ensure feedback styling integrates seamlessly with form field components and overall form design

