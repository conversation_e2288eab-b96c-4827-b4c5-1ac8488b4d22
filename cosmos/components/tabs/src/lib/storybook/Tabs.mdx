import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as TabsStories from './Tabs.stories';

<Meta of={TabsStories} />

<Title />

<Description />

<Primary />

<Controls of={TabsStories.Playground} />

## Import

```jsx
import { Tabs } from '@drata/cosmos-tabs';
```

## Examples

### Tabs overflow

<Canvas of={TabsStories.Variant} />

## Accessibility

This component is built using the [Radix Primitives Tabs](https://www.radix-ui.com/primitives/docs/components/tabs), and follows the [WAI-ARIA Tabs Pattern](https://www.w3.org/WAI/ARIA/apg/patterns/tabs/).

### Keyboard Support

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <th>
                <kbd>Tab</kbd>
            </th>
            <th>
                When focus moves onto the tabs, focuses the active trigger. When
                a trigger is focused, moves focus to the active content.
            </th>
        </tr>
        <tr>
            <th>
                <kbd>Arrow right</kbd>
            </th>
            <th>
                Moves focus to the next trigger and activates its associated
                content.
            </th>
        </tr>
        <tr>
            <th>
                <kbd>Arrow left</kbd>
            </th>
            <th>
                Moves focus to the previous trigger and activates its associated
                content.
            </th>
        </tr>
        <tr>
            <th>
                <kbd>Home</kbd>
            </th>
            <th>
                Moves focus to the first trigger and activates its associated
                content.
            </th>
        </tr>
        <tr>
            <th>
                <kbd>End</kbd>
            </th>
            <th>
                Moves focus to the last trigger and activates its associated
                content.
            </th>
        </tr>
    </tbody>
</table>

## 🟢 When to use the component

- **Lengthy content with clear groupings** - When lengthy content has clear groupings to move between different views within the same context
- **Same level of hierarchy** - For organizing content at the same level of importance and hierarchy
- **Space-efficient layouts** - When you need to display multiple content areas without taking up vertical space
- **Clear content sections** - When content can be organized into distinct, self-contained sections

## ❌ When not to use the component

- **Single content area** - When there is only one tab needed, use other components like cards or simple layouts
- **Navigation between pages** - For navigation, consider Link or NavigationMenu components instead
- **Sequential content** - For step-by-step processes, consider wizard or stepper components
- **Simultaneous content viewing** - To see multiple content areas at the same time, consider Card or other layout components
- **Few content groupings** - When there are few content groupings. The fewer tabs, the better
- **Long or complex labels** - When content cannot be labeled concisely. Short and concise labels are best
- **Unequal content importance** - When content has unequal importance as the default tab content will receive more attention than other tab's content
- **Comparison needs** - When users need to simultaneously see information presented under different tabs

**Consider alternatives:**
- **Accordion** - Works well with longer labels, can expose multiple groups of content at the same time, and work well for short content groupings
- **Page navigation** - Allows for a quick overview and navigation of a long page of content on a single page

## 🛠️ How it works

Tabs allow users to move between different views within the same context at the same level of hierarchy.

**Component behavior:**
- **Full-width layout** - Tabs will fill 100% of the parent container's width
- **Overflow handling** - When there are too many tabs to fit within the TabList, a horizontal scrollbar will help the user navigate hidden tabs
- **User-controlled switching** - Don't trigger a Tab change via an external action, such as a submit or next button. Tab changes should only be triggered by the Tab itself as that is the expected user interaction

**Tab structure:**
- Built on Radix UI's Tabs primitive for accessibility and keyboard support
- Uses internal state management with optional external control via `onTabChange`
- Automatically selects first tab if no `defaultTabId` is provided
- Supports metadata numbers to indicate alerts within a tab section and help users identify high priority content

**Overflow behavior:**
- Shows left/right scroll buttons when tabs exceed available space
- Provides smooth scrolling navigation between hidden tabs
- Maintains accessibility during overflow with proper focus management

### Usability

**Tab interaction:**
- **Direct user control** - Don't trigger tab changes via external actions like submit or next buttons
- **Expected behavior** - Tab changes should only be triggered by the tab itself as users expect this interaction pattern
- **Overflow navigation** - When tabs don't fit, users can scroll horizontally to access hidden tabs

**Content organization:**
- **Clear groupings** - Organize content into logical, distinct sections that users can easily understand
- **Consistent structure** - Keep similar types of content in the same position across different tab sets
- **Self-contained sections** - Each tab should contain complete content that doesn't require other tabs

### Content

Tabs should be short and concise, and a good indication of what content the user can expect to find within the TabPanel. They should not consist of full sentences.

**Tab labeling:**
- **Clear differentiation** - Be clearly labeled to help differentiate the different sections beneath them
- **Scannable text** - Have short and scannable labels, generally kept to single word
- **Contextual relevance** - Relate to the section the user is on. Imagine the page section title is an invisible noun after the tab. No need to repeat the core site section in each tab label. E.g. in Vendors, the tabs should be Current and Prospective instead of Current Vendors, Prospective Vendors

**Metadata usage:**
- **Priority indicators** - Metadata numbers can be used with tabs to indicate alerts within a tab section and help users identify high priority content
- **Consistent application** - Apply metadata consistently across similar tab implementations

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper tablist (`role="tablist"`) and tab panels (`role="tabpanel"`)
- Full keyboard navigation support with arrow keys, Home/End keys, and Tab key focus management
- Proper ARIA attributes including `role="tab"`, `aria-selected`, and `aria-controls` for screen reader support
- Focus management that maintains logical tab order and handles overflow scrolling
- High contrast support that works with system accessibility preferences
- Touch target sizing that meets accessibility guidelines for interactive elements

**Development responsibilities:**
- Ensure tab content is properly structured with headings and landmarks for screen readers
- Handle focus appropriately when programmatically changing tabs or loading dynamic content
- Provide appropriate feedback when tab content is loading or updating
- Handle and communicate errors within tab content accessibly

**Design responsibilities:**
- Clearly distinguish active tabs from inactive ones with sufficient visual contrast
- Provide clear focus indicators for keyboard navigation that meet contrast requirements
- Ensure tabs remain usable and accessible across different screen sizes
- Design tab content with proper spacing and hierarchy for screen reader navigation

