import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as RadioFieldGroupStories from './RadioFieldGroup.stories';

<Meta of={RadioFieldGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={RadioFieldGroupStories.Playground} />

## Import

```jsx
import { RadioFieldGroup } from '@drata/cosmos-radio-field-group';
```

## Props

### `defaultValue`

Passing the value of a Radio option to `defaultValue` will set that option as the initially selected value.

<Canvas of={RadioFieldGroupStories.DefaultValue} />

### `cosmosUseWithCaution_forceOptionOrientation`

By default, RadioFieldGroup uses vertical layout for all radio groups to ensure optimal readability and accessibility.

In the rare case that this needs to be overridden, use `cosmosUseWithCaution_forceOptionOrientation` to manually set the orientation.

<Canvas of={RadioFieldGroupStories.USEWITHCAUTION_ForceOptionOrientation} />
<Controls
    of={RadioFieldGroupStories.USEWITHCAUTION_ForceOptionOrientation}
    include={['cosmosUseWithCaution_forceOptionOrientation']}
/>

## 🟢 When to use the component

- **Single choice selections** - When users need to select exactly one option from a predefined set of mutually exclusive choices
- **Settings and preferences** - For configuration options where only one setting can be active at a time
- **Status selections** - When users need to choose a single status, priority, or category
- **Agreement responses** - For yes/no questions or agreement scales with mutually exclusive options
- **Method selections** - When users choose between different approaches, methods, or processes

## ❌ When not to use the component

- **Multiple selections needed** - Use CheckboxFieldGroup when users can select multiple options simultaneously
- **Long lists of options** - Use SelectField or ComboboxField for more than 3-4 options to save space
- **Binary choices** - Use ToggleField or single CheckboxField for simple on/off or yes/no decisions
- **Searchable options** - Use ComboboxField when users need to search through many options
- **Dynamic option lists** - Use SelectField when options are loaded dynamically or change frequently
- **Hierarchical choices** - Use nested SelectFields or custom components for hierarchical option structures

## 🛠️ How it works

The RadioFieldGroup component manages a group of related radio options, allowing users to select only one option from a set of predefined options with vertical layout and complete form field functionality.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration with `role="radiogroup"`
- **Vertical layout** - Uses vertical orientation with 12px gap spacing for optimal readability and accessibility
- **RadioField composition** - Renders individual RadioField components with proper group coordination and ARIA relationships
- **Controlled state** - Uses `value` prop to control which option is selected across the entire group

**Option management:**
- **RadioOption type** - Each option includes required `label` and `value`, plus optional `helpText`, `disabled`, and `readOnly` properties
- **Value normalization** - Uses `normalizeValue` helper to create consistent IDs by removing spaces from option values
- **Individual control** - Each option can be independently disabled or made read-only while maintaining group behavior
- **Unique identification** - Automatic generation of unique IDs for each option based on group ID and normalized values

**State coordination:**
- **Single selection** - Only one radio option can be selected at a time across the entire group
- **Controlled component** - Parent component manages selected value via `value` prop and `onChange` callback
- **Change handling** - `onChange` receives the standard React ChangeEvent with selected option's value
- **Group validation** - Validation applies to the entire group rather than individual radio buttons

**Accessibility implementation:**
- **Radiogroup semantics** - FormField renders with `role="radiogroup"` for proper screen reader context
- **ARIA relationships** - Each RadioField gets proper `aria-labelledby` and `aria-describedby` connections
- **Keyboard navigation** - Standard radio group keyboard behavior with arrow keys for option navigation
- **Label association** - Group label properly describes the set of options for screen reader users

**Styling and spacing:**
- **Grid positioning** - Uses CSS Grid for precise layout control and responsive behavior
- **Force override** - `cosmosUseWithCaution_forceOptionOrientation` prop allows manual layout control for exceptional cases only
- **Consistent theming** - Inherits design system tokens for colors, spacing, and typography

### Usability

**Option design:**
- **Mutually exclusive choices** - Ensure all options represent truly exclusive alternatives
- **Complete option set** - Provide options that cover all reasonable user scenarios
- **Logical ordering** - Arrange options in logical order (frequency, importance, alphabetical, etc.)
- **Balanced options** - Avoid having one obviously "correct" choice unless intentional

**Layout considerations:**
- **Vertical arrangement** - Provides optimal readability and enables predictable top-to-bottom scanning patterns
- **Space efficiency** - Accommodates longer labels and help text effectively across all screen sizes
- **Consistent patterns** - Maintain consistent orientation choices across similar form sections

**State management:**
- **Default selection** - Consider providing a sensible default value for better user experience
- **Required handling** - Use `required` prop for validation when selection is mandatory
- **Change detection** - Handle `onChange` events to update application state appropriately
- **Validation integration** - Coordinate with form validation systems for error handling

**Content strategy:**
- **Group labeling** - Use clear group labels that describe the choice being made
- **Option clarity** - Each option label should be self-explanatory within the group context
- **Help text usage** - Use group help text for overall guidance, option help text for specific clarification
- **Consistent terminology** - Maintain consistent language patterns across related radio groups

### Content

**Group labeling:**
- **Descriptive titles** - Use group labels that clearly explain what users are choosing between
- **Question format** - Frame labels as questions when appropriate ("Which option do you prefer?")
- **Context provision** - Ensure labels provide enough context for informed decision-making
- **Action clarity** - Make it clear what happens when an option is selected

**Option content:**
- **Parallel structure** - Use consistent grammatical structure across all options in a group
- **Distinctive labels** - Each option should be clearly different and easily distinguishable
- **Concise clarity** - Keep option labels brief while maintaining specificity and clarity
- **Value alignment** - Ensure option labels accurately represent their underlying values

**Help text strategy:**
- **Group guidance** - Use group help text to explain the overall choice or its consequences
- **Option clarification** - Use individual option help text to explain complex or technical options
- **Example provision** - Provide examples or scenarios to help users understand options
- **Consequence explanation** - Explain what happens when specific options are selected

**Validation messaging:**
- **Group-level errors** - Validation errors apply to the entire group, not individual options
- **Clear requirements** - Explain when selection is required and why
- **Error recovery** - Provide clear guidance on how to resolve validation errors
- **Contextual feedback** - Error messages should relate to the specific choice being made

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper `role="radiogroup"` and individual radio button elements for full screen reader support
- Complete keyboard navigation including Tab to enter group, arrow keys to navigate between options, and Space to select
- Automatic ARIA relationships with `aria-labelledby` connecting group label and `aria-describedby` for help text and validation feedback
- High contrast support that works with system accessibility preferences and meets WCAG AA standards across all themes
- Scalable typography and spacing that works with browser zoom up to 200% magnification
- Focus management with clear focus indicators and logical tab order through radio options

**Development responsibilities:**
- Provide clear, descriptive `label` prop that explains what choice users are making
- Use meaningful `name` prop that creates proper form field grouping for submission
- Ensure each option has unique, stable `value` props that work with form processing systems
- Implement proper `onChange` handling that updates application state when selection changes
- Provide helpful group-level `helpText` that guides users toward making informed choices
- Handle validation appropriately with clear error messages when selection is required but missing

**Design responsibilities:**
- Provide sufficient color contrast for radio buttons, labels, help text, and validation feedback across all themes
- Design clear visual hierarchy that shows the relationship between group label, individual options, and help text
- Ensure focus indicators are clearly visible and meet contrast requirements for keyboard navigation
- Create consistent visual patterns for radio group styling that work across different orientations and option counts
- Design appropriate spacing and sizing that works across different screen sizes and container widths
- Ensure selected, unselected, disabled, and validation states are clearly distinguishable through visual design

