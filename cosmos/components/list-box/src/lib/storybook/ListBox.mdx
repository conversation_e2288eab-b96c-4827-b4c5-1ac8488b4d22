import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';

import * as ListBoxStories from './ListBox.stories';

<Meta of={ListBoxStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos component, you are probably looking for a different component."
/>
<br />

<Description />

<Primary />

<Controls of={ListBoxStories.Playground} />

## Import

```jsx
import { ListBox } from '@cosmos/components/list-box';
```

## Props

### `emptyState`

The `emptyState` prop accepts a ReactNode which will be shown if no items are available.

<Canvas of={ListBoxStories.EmptyState} />

## `isLoading`

If `true`, a loading spinner will render instead of items.

<Canvas of={ListBoxStories.Loading} />

## Examples

### No Groups

An example of the ListBox component when no items should be rendered in a group.

<Canvas of={ListBoxStories.NoGroups} />

### Groups Only

An example of the ListBox component when all items are part of a group.

<Canvas of={ListBoxStories.GroupsOnly} />

### Infinite Scroll

<Canvas of={ListBoxStories.InfiniteScroll} />

### With Slot

An example of the ListBox component when the items have slot with custom components.

<Canvas of={ListBoxStories.WithSlot} />

## 🟢 When to use the component

- **Select and combobox components** - As the foundational list component for dropdown selections and autocomplete
- **Option lists** - When displaying a list of selectable items with consistent styling and ARIA support
- **Grouped options** - When options need to be organized into logical groups with headers and dividers
- **Rich list items** - When list items need icons, avatars, descriptions, or metadata in start/end slots
- **Searchable lists** - As part of combobox or autocomplete implementations with proper option roles
- **Multi-select scenarios** - When users need to select multiple items with checkbox indicators
- **Lazy-loaded lists** - For large datasets that load incrementally with loading states and sentinels

## ❌ When not to use the component

- **Simple text lists** - Use the List component for basic unordered or ordered lists without selection
- **Navigation menus** - Use NavigationMenu or Dropdown components for navigation actions
- **Data tables** - Use DataTable for tabular data with sorting and filtering capabilities
- **Action menus** - Use Dropdown component for action-oriented menu items
- **Static content** - When items don't need selection, ARIA roles, or interaction functionality

## 🛠️ How it works

The ListBox component provides a flexible foundation for building accessible selectable lists with rich content support and proper ARIA implementation.

**Component structure:**
- **ListBox** - Root container (`<ul>`) that renders items and groups with `listbox` role and lazy loading support
- **ListBoxItem** - Individual selectable items (`<li>`) with `option` role, tooltip integration, and read-only state support
- **ListBoxGroup** - Groups related items with optional headers, top/bottom dividers, and proper group semantics
- **StructuredListItem** - Content component for consistent item layout with label, description, and slot support

**Item data structure (ListBoxItemData):**
- **Core properties** - `id` (required), `label` (required), `value` (optional)
- **Content slots** - `startSlot` and `endSlot` for ReactNode content like icons or metadata
- **Description support** - Optional `description` ReactNode for additional context
- **Color schemes** - Configurable `colorScheme` for text styling
- **Extensible data** - Supports additional properties via index signature

**Group data structure (ListBoxGroupData):**
- **Group identification** - `groupHeader` for the group title
- **Item collection** - `items` array containing ListBoxItemData objects
- **Visual separation** - Automatic divider management between groups

**Advanced features:**
- **Read-only items** - Items with `isReadOnly` prop and required `readOnlyTooltip` for explanation
- **Checkbox display** - `showCheckbox` prop for multi-select scenarios with visual selection state
- **Caret indicators** - `withCaret` prop for expandable or navigational items
- **Lazy loading** - Built-in support with `fetchMoreItems`, `hasMore`, and loading states
- **Empty states** - Configurable empty state content when no items are present

### Usability

**Item organization:**
- **Logical grouping** - Use ListBoxGroup to organize related options with descriptive headers
- **Consistent labeling** - Provide clear, scannable labels that describe each option accurately
- **Meaningful descriptions** - Use description prop to provide additional context when helpful
- **Visual hierarchy** - Use startSlot/endSlot strategically to create clear visual patterns

**Selection patterns:**
- **Single selection** - Default behavior with `aria-selected` state management
- **Multi-selection** - Enable `showCheckbox` when users need to select multiple items
- **Read-only display** - Use `isReadOnly` with explanatory tooltips for non-selectable context items
- **Loading feedback** - Provide clear loading states during lazy loading operations

### Content

**Item structure:**
- **Descriptive labels** - Use clear, specific labels that help users identify options quickly
- **Contextual descriptions** - Provide additional details that clarify the option without overwhelming
- **Consistent terminology** - Maintain consistent language patterns across similar lists
- **Appropriate length** - Keep labels concise while providing sufficient context for decision-making

**Slot content:**
- **Start slots** - Use for icons, avatars, or status indicators that provide immediate context
- **End slots** - Use for metadata, secondary actions, or status information
- **Meaningful icons** - Ensure slot content enhances rather than replaces textual information
- **Visual balance** - Ensure slot content doesn't overwhelm the primary label and description

**Grouping strategy:**
- **Logical categories** - Group items in ways that match user mental models and workflows
- **Clear headers** - Use descriptive group headers that explain the categorization clearly
- **Appropriate sizing** - Limit group sizes to maintain scannability and cognitive load
- **Consistent structure** - Use similar grouping patterns across related components

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper ARIA roles (`listbox`, `option`, `group`) for screen reader navigation
- Full keyboard navigation support including arrow keys, Home/End, and proper focus management
- Screen reader announcements for selection changes, group navigation, and read-only item explanations
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators and logical tab order through items and groups
- Touch target sizing that meets accessibility guidelines with proper interactive element spacing

**Development responsibilities:**
- Provide unique, stable `id` values for all ListBoxItemData objects to ensure proper selection tracking
- Ensure item labels are descriptive and meaningful for screen reader users without visual context
- Implement proper `readOnlyTooltip` text that explains why items are non-interactive
- Handle dynamic content updates appropriately with proper ARIA live regions for loading states
- Ensure slot content (startSlot/endSlot) includes appropriate alt text or ARIA labels when needed
- Provide meaningful `emptyState` content that guides users when no options are available

**Design responsibilities:**
- Provide sufficient color contrast for all item states, color schemes, and read-only indicators across themes
- Design clear visual hierarchy that distinguishes between items, groups, selection states, and interactive elements
- Ensure focus indicators are clearly visible and meet contrast requirements for all item types and states
- Create consistent visual patterns for similar list types and selection behaviors across the application
- Design appropriate spacing and sizing for touch targets, content density, and group separation across screen sizes
- Ensure start/end slot content enhances accessibility rather than creating visual-only information

