import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';

import * as SelectStories from './Select.stories';

<Meta of={SelectStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for SelectField."
/>
<br />

<Description />

<Primary />

<Controls of={SelectStories.Playground} />

## Import

```jsx
import { Select } from '@drata/cosmos-select';
```

## Props

### defaultValue

<Canvas of={SelectStories.DefaultValue} />

## 🟢 When to use the component

- **Building Cosmos components** - This is a foundational component intended only for creating other Cosmos form components
- **Custom form component development** - When developing specialized form components that need basic dropdown functionality
- **Component library extensions** - For extending the Cosmos design system with new form field variants

## ❌ When not to use the component

- **Standard form fields** - Use SelectField for typical form implementations with labels, validation, and help text
- **Application development** - This is a foundational component; use higher-level form components for direct application use
- **Multi-select scenarios** - Use ComboboxField for multiple selection capabilities
- **Searchable dropdowns** - Use ComboboxField when users need to search through options
- **Complex option layouts** - Use ComboboxField for options with rich content or descriptions

## 🛠️ How it works

The Select component provides foundational dropdown selection functionality for building other Cosmos form components, allowing users to choose one option from a short list with keyboard navigation and accessibility support.

**Component architecture:**
- **Downshift integration** - Uses `useSelect` hook for robust dropdown state management and accessibility
- **Popover positioning** - Leverages Popover component with Floating UI for intelligent dropdown placement
- **ListBox rendering** - Displays options using the ListBox component with proper ARIA relationships
- **SelectTrigger interface** - Custom trigger component that displays selected value and dropdown indicator

**Selection and state:**
- **Single selection** - Users can select exactly one option with controlled `value` prop and `onChange` callback
- **Default value** - Supports `defaultValue` prop for initial selection state
- **Placeholder support** - Shows `placeholderText` when no option is selected

**Option management:**
- **ListBoxItems structure** - Accepts options as `ListBoxItems` array supporting both individual options and option groups
- **Flat option processing** - Uses `useFlatOptions` and `useDownshiftOptions` hooks to handle nested structures and apply proper item props
- **Unique identification** - Each option gets unique IDs based on the component's base ID

**Positioning and interaction:**
- **Intelligent positioning** - Uses Floating UI middleware (flip, shift, size) for optimal dropdown placement with viewport awareness
- **Width matching** - Dropdown automatically matches trigger width with configurable minimum width
- **Full keyboard support** - Arrow keys for navigation, Enter/Space for selection, Escape to close
- **Focus management** - Maintains proper focus on trigger and within dropdown

**States and feedback:**
- **Loading support** - `isLoading` prop shows spinner instead of options with customizable `loaderLabel`
- **Validation integration** - Supports `feedbackType` prop for validation state styling
- **Disabled states** - Handles both `disabled` and `readOnly` states with appropriate styling and behavior
- **Portal rendering** - Dropdown renders in a portal to avoid z-index and overflow issues

### Usability

**Component integration:**
- **Foundation layer** - Designed as a building block for higher-level form components
- **Minimal interface** - Provides essential dropdown functionality without form field overhead
- **Flexible styling** - Accepts `gridArea` and `feedbackType` props for layout and styling integration
- **Event coordination** - Supports custom event handlers for integration with parent components

**Option design:**
- **Clear labeling** - Each option should have a clear, descriptive label
- **Logical grouping** - Use option groups to organize related choices when appropriate
- **Reasonable limits** - Keep option lists manageable (consider ComboboxField for longer lists)
- **Consistent structure** - Maintain consistent option format across similar selects

**State management:**
- **Controlled usage** - Always use with controlled `value` prop and `onChange` handler
- **Default selection** - Use `defaultValue` for initial state when appropriate
- **Validation integration** - Coordinate with parent component's validation system
- **Loading states** - Show loading state during async option fetching

**Accessibility considerations:**
- **Proper labeling** - Ensure `aria-labelledby` connects to appropriate label element
- **Description support** - Use `aria-describedby` for help text or error message connections
- **Required indication** - Use `required` prop for form validation requirements
- **Keyboard efficiency** - Component provides full keyboard navigation out of the box

### Content

**Option content:**
- **Descriptive labels** - Each option should clearly describe what it represents
- **Parallel structure** - Use consistent grammatical structure across all options
- **Concise text** - Keep option labels brief while maintaining clarity
- **Meaningful values** - Ensure option values are appropriate for form submission and processing

**Placeholder guidance:**
- **Action-oriented** - Use placeholders that guide user action ("Select an option", "Choose...")
- **Context-specific** - Tailor placeholder text to the specific selection context
- **Avoid defaults** - Don't use placeholders as default selections unless intentional
- **Clear expectations** - Make it obvious that user action is required

**Loading states:**
- **Informative labels** - Use descriptive `loaderLabel` text that explains what's loading
- **Context awareness** - Tailor loading messages to the specific data being fetched
- **User feedback** - Provide clear indication that the system is working
- **Timeout handling** - Consider error states for failed or slow-loading options

**Grouping strategy:**
- **Logical organization** - Group related options together when using option groups
- **Clear group labels** - Use descriptive group labels that explain the categorization
- **Balanced groups** - Avoid having groups with significantly different option counts
- **Flat alternatives** - Consider flat lists when grouping doesn't add clear value

### Accessibility

**What the design system provides:**
- Complete keyboard navigation with arrow keys for option selection, Enter/Space to select, and Escape to close dropdown
- Semantic HTML structure with proper ARIA roles including `role="listbox"` for options and `role="combobox"` for trigger
- Automatic ARIA relationships with `aria-labelledby`, `aria-describedby`, and `aria-controls` connecting trigger to dropdown
- High contrast support that works with system accessibility preferences and meets WCAG AA standards for all states
- Screen reader announcements for selection changes, dropdown state, and option navigation
- Focus management that maintains logical tab order and clear focus indicators throughout interaction

**Development responsibilities:**
- Provide meaningful `aria-labelledby` prop that connects to an appropriate label element for the select
- Use `aria-describedby` prop to connect help text, instructions, or error messages when present
- Ensure `id` prop is unique and stable across renders for proper ARIA relationship establishment
- Implement proper `onChange` handling that updates application state when selection changes
- Use `required` prop appropriately when selection is mandatory for form submission
- Provide clear, descriptive option labels that work well with screen reader announcement patterns

**Design responsibilities:**
- Provide sufficient color contrast for trigger, dropdown, options, and focus states across all themes
- Design clear visual hierarchy that distinguishes between trigger, dropdown, selected options, and hover states
- Ensure focus indicators are clearly visible and meet contrast requirements for keyboard navigation
- Create consistent visual patterns for select styling that work across different contexts and container sizes
- Design appropriate spacing and sizing that works with browser zoom up to 200% magnification
- Ensure selected, unselected, disabled, loading, and error states are clearly distinguishable through visual design

