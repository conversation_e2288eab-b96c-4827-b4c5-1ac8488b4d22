import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as DropdownStories from './Dropdown.stories';

<Meta of={DropdownStories} />

<Title />

<Banner
    severity="warning"
    title="Consider using SchemaDropdown instead!"
    body={<>Most use cases are better served by <Link href="/?path=/docs/actions-schemadropdown--docs" label="SchemaDropdown" />, which provides a simpler API. Only use this composable Dropdown when you need full control over structure or custom content integration.</>}
/>

<Description />

<Primary />

<Controls of={DropdownStories.Playground} />

## Import

```jsx
import { Dropdown } from '@cosmos/components/dropdown';
```

## Examples

### Basic Usage

<Canvas of={DropdownStories.Playground} />

### With AppLink Integration

This example shows how to integrate navigation links within dropdown items:

<Canvas of={DropdownStories.WithAppLink} />

### With Groups

Organize related items using groups with headers and dividers:

<Canvas of={DropdownStories.WithGroups} />

### With Submenus

Create nested menus for complex hierarchical actions:

<Canvas of={DropdownStories.WithSubMenus} />

### Icon-Only Trigger

Use an icon-only button as the dropdown trigger:

<Canvas of={DropdownStories.IconOnlyTrigger} />
## 🟢 When to use the component

- **Custom dropdown structures**: When you need full control over the dropdown layout and content
- **Complex interactions**: When dropdown items need custom behavior like navigation links or complex actions
- **Integration with other components**: When dropdown items need to wrap other components like AppLink

## ❌ When not to use the component

- **Simple action lists**: Use `SchemaDropdown` for straightforward lists of actions with consistent styling
- **Form inputs**: Use `SelectField` or `ComboboxField` for form-based selections
- **Data-driven menus**: Use `SchemaDropdown` when you have a simple array of items to display

## 🛠️ How it works

This component is built using [Radix UI Dropdown Menu](https://www.radix-ui.com/primitives/docs/components/dropdown-menu) primitives and follows the [Menu Button WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/menu-button/).

**Component structure:**
- **Dropdown** - Root container that manages open/close state using Radix UI primitives
- **Dropdown.Trigger** - The element that triggers the dropdown (usually a Button)
- **Dropdown.Content** - The dropdown panel container with Portal for positioning and collision detection
- **Dropdown.Item** - Individual dropdown items that wrap content like StructuredListItem for consistent styling
- **Dropdown.Group** - Groups related items with optional headers and dividers using ListBoxGroup
- **Dropdown.SubMenu** - Container for nested submenus with one level of nesting support
- **Dropdown.SubMenuTrigger** - Trigger for opening a submenu
- **Dropdown.SubMenuContent** - Content container for submenu items

**Interaction behavior:**
- **State management** - Supports both controlled (`open`/`onOpenChange`) and uncontrolled (`defaultOpen`) patterns
- **Positioning** - Automatic positioning with collision detection, configurable alignment and side placement
- **Portal rendering** - Content renders in a portal for proper z-index layering and positioning
- **Focus management** - Maintains proper focus order and keyboard navigation between items

**Content integration:**
- **Flexible content** - Items can wrap any React components including AppLink for navigation
- **Consistent styling** - Use StructuredListItem within Dropdown.Item for design system consistency
- **Custom actions** - Items support custom onClick handlers and navigation patterns
- **Accessibility** - Built-in ARIA attributes and keyboard navigation following WAI-ARIA menu button pattern

### Usability

**Menu organization:**
- **Logical grouping** - Use Dropdown.Group to organize related actions with descriptive headers
- **Clear hierarchy** - Limit submenu depth to one level for better usability and navigation
- **Meaningful labels** - Provide clear, action-oriented labels for all menu items
- **Appropriate triggers** - Use buttons or other interactive elements as triggers with proper labeling

**Navigation patterns:**
- **AppLink integration** - Wrap navigation items with AppLink to maintain proper routing behavior
- **Action consistency** - Group similar actions together and use consistent terminology
- **Loading states** - Handle loading states appropriately when dropdown actions trigger async operations

### Content

**Menu structure:**
- **Scannable organization** - Organize items in logical groups that match user mental models
- **Clear action labels** - Use specific, action-oriented text rather than generic terms
- **Consistent terminology** - Maintain consistent language patterns across similar dropdowns
- **Appropriate icons** - Use meaningful icons that reinforce rather than replace text labels

**Item organization:**
- **Related grouping** - Group related actions using Dropdown.Group with descriptive headers
- **Logical order** - Arrange items in order of frequency of use or logical workflow sequence
- **Destructive actions** - Place destructive actions (like delete) at the bottom, separated from other actions

### Accessibility

**What the design system provides:**
- Semantic HTML structure following WAI-ARIA menu button design pattern with proper roles and attributes
- Full keyboard navigation support including arrow keys, Enter/Space activation, and Escape to close
- Screen reader announcements for menu state changes and item selection
- Focus management that maintains logical tab order and handles submenu navigation
- High contrast support that works with system accessibility preferences
- Touch target sizing that meets accessibility guidelines for interactive elements

**Development responsibilities:**
- Provide meaningful data-id attributes for testing and analytics tracking
- Ensure dropdown items have clear, descriptive labels for screen reader users
- Handle focus appropriately when dropdown actions trigger navigation or state changes
- Provide proper error handling and feedback when dropdown actions fail
- Ensure AppLink integration maintains proper navigation announcements

**Design responsibilities:**
- Provide sufficient color contrast for all menu items and states across different themes
- Design clear visual hierarchy that distinguishes between groups, items, and submenus
- Ensure focus indicators are clearly visible and meet contrast requirements
- Design appropriate spacing and sizing for touch targets across different screen sizes
- Create consistent visual patterns for similar dropdown types across the application

#### Keyboard Support

| Key | Function |
|-----|----------|
| <kbd>Space</kbd> | When focus is on trigger, opens the dropdown. When focus is on an item, activates the item. |
| <kbd>Enter</kbd> | When focus is on trigger, opens the dropdown. When focus is on an item, activates the item. |
| <kbd>ArrowDown</kbd> | When focus is on trigger, opens the dropdown. When focus is on an item, moves focus to the next item. |
| <kbd>ArrowUp</kbd> | When focus is on an item, moves focus to the previous item. |
| <kbd>ArrowRight</kbd> | When focus is on a submenu trigger, opens the submenu. |
| <kbd>ArrowLeft</kbd> | When focus is on a submenu item, closes the submenu and moves focus to the submenu trigger. |
| <kbd>Escape</kbd> | Closes the dropdown and moves focus to the trigger. |

## Comparison with SchemaDropdown

| Feature | Dropdown (Composable) | SchemaDropdown |
|---------|----------------------|----------------|
| **Flexibility** | Full control over structure | Predefined structure |
| **Custom Content** | Any React components | Limited to predefined item types |
| **AppLink Integration** | Easy with Dropdown.Item wrapper | Not supported |
| **Submenus** | Full support with Dropdown.SubMenu | Limited support |
| **Groups** | Full control with Dropdown.Group | Automatic grouping |
| **Styling** | Use StructuredListItem for consistency | Built-in styling |
| **Use Case** | Complex, custom dropdowns | Simple action lists |

## Best Practices

1. **Use StructuredListItem** for consistent styling within Dropdown.Item
2. **Provide meaningful data-id attributes** for testing and analytics
3. **Use slot prop instead of deprecated startIconName/endIconName** for icons
4. **Keep submenu depth to one level** for better usability
5. **Use AppLink wrapper** for navigation items to maintain proper routing
6. **Group related actions** using Dropdown.Group with descriptive headers
