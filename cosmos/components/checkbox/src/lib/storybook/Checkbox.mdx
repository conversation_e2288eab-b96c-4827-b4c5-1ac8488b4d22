import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as CheckboxStories from './Checkbox.stories';

<Meta of={CheckboxStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />

<Primary />

<Controls of={CheckboxStories.Playground} />

## Import

```jsx
import { Checkbox } from '@drata/cosmos-checkbox';
```

## Props

### `defaultChecked`

The `defaultChecked` prop defines the initial `checked` state of the component.

<Canvas of={CheckboxStories.DefaultChecked} />

## Examples

### Adding a tooltip to a checkbox

<Canvas of={CheckboxStories.TooltipStory} />


## 🟢 When to use the component

- **Building form components** - As a foundational element for creating other Cosmos form components like CheckboxField
- **Binary state selection** - When users need to make a simple true/false or on/off choice
- **Multiple independent selections** - When users can select zero, one, or multiple options from a list where choices don't affect each other
- **Table row selection** - As the foundational component for enabling row selection in data tables and enabling bulk actions

## ❌ When not to use the component

- **End-user forms** - Use [CheckboxField](https://cosmos.drata.com/?path=/docs/forms-checkboxfield--docs) or [CheckboxFieldGroup](https://cosmos.drata.com/?path=/docs/forms-checkboxfieldgroup--docs) instead, which provides proper labeling and form integration
- **Mutually exclusive choices** - Use [RadioField](https://cosmos.drata.com/?path=/docs/forms-radiofield--docs) or [RadioFieldGroup](https://cosmos.drata.com/?path=/docs/forms-radiofield--docs) when only one option can be selected
- **Complex selections requiring explanation** - Consider [ChoiceCard](https://cosmos.drata.com/?path=/docs/forms-choicecard--docs) or [ChoiceCardGroup](https://cosmos.drata.com/?path=/docs/forms-choicecardgroup--docs) for options that need additional context
- **On/off states for immediate actions** - Use [Toggle](https://cosmos.drata.com/?path=/docs/forms-toggle--docs) component for settings that take effect immediately

## 🛠️ How it works

A low-level checkbox input component that provides the foundational checkbox functionality for building other Cosmos form components. Handles checked, unchecked, and indeterminate states with proper accessibility support.

### Usability

**State management:**
- Supports controlled and uncontrolled usage patterns
- Handles three states: checked, unchecked, and indeterminate
- Provides `defaultChecked` for initial state in uncontrolled mode
- Uses `checked` prop for controlled implementations

**Interaction behavior:**
- Click or tap to toggle between checked and unchecked states
- Space key activation when focused
- Visual feedback for hover, focus, and active states
- Maintains focus after state changes for keyboard users

**Integration patterns:**
- Designed to be wrapped by higher-level form components
- Provides essential checkbox functionality without form-specific features
- Can be combined with custom labeling and validation logic
- Supports tooltip integration for additional context

### Content

**Implementation guidelines:**
- This component provides the checkbox input only - labels and descriptions should be handled by parent components
- When building custom form components, associate labels properly using `htmlFor` and `id` attributes
- Ensure any custom labeling follows the established content guidelines for form fields
- Use clear, action-oriented language when this checkbox represents an action or agreement

### Accessibility

This component is built using the [Radix Primitives Checkbox](https://www.radix-ui.com/primitives/docs/components/checkbox), and follows the [tri-state Checkbox WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/checkbox).

**What the design system provides:**
- Proper ARIA attributes for checkbox state and properties
- Keyboard navigation support with Space key activation
- Focus management and visible focus indicators
- Screen reader announcements for state changes
- Semantic HTML structure with proper form associations
- High contrast mode compatibility
- Touch target sizing meets accessibility guidelines (minimum 44px)

**Development responsibilities:**
- Associate the checkbox with proper labels using `htmlFor` and `id` attributes
- Provide accessible error messaging when used in form validation
- Ensure proper form submission handling and value management
- Test keyboard navigation across different browsers and assistive technologies
- Implement accessible loading or disabled states when needed

**Design responsibilities:**
- Design clear visual states for checked, unchecked, and indeterminate modes
- Ensure sufficient contrast ratios for all checkbox states
- Create accessible error states that are clearly associated with the checkbox
- Don't rely solely on color to convey checkbox state or validation status
- Maintain adequate spacing around the checkbox for easy interaction

#### Keyboard Support:

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <th>
                <kbd>Space</kbd>
            </th>
            <th>Checks/unchecks the checkbox</th>
        </tr>
    </tbody>
</table>