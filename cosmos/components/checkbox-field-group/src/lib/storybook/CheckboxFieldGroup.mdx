import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as CheckboxFieldGroupStories from './CheckboxFieldGroup.stories';

<Meta of={CheckboxFieldGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={CheckboxFieldGroupStories.Playground} />

## Import

```jsx
import { CheckboxFieldGroup } from '@drata/cosmos-checkbox-field-group';
```

## 🟢 When to use the component

- **Multiple selections** - When users need to select zero, one, or multiple options from a predefined list
- **Filtering interfaces** - When users need to apply multiple filter criteria at once
- **Bulk operations** - When users need to select multiple items for batch actions

## ❌ When not to use the component

- **Single selection only** - Use RadioFieldGroup when only one option can be selected
- **Binary choices** - Use a single CheckboxField for simple yes/no decisions
- **Large option sets** - Consider ComboboxField with multi-select for 10+ options
- **Complex option descriptions** - Use ChoiceCardGroup when options need detailed explanations
- **Space-constrained layouts** - Consider ComboboxField or <PERSON><PERSON>ield for limited space

## 🛠️ How it works

The CheckboxFieldGroup component manages a collection of related checkbox options with shared validation, automatic layout optimization, and optional "Select All" functionality.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, and validation feedback
- **Select All checkbox** - Optional master checkbox that controls all individual options
- **Individual checkboxes** - Each option renders as a CheckboxField with shared styling and behavior
- **Responsive layout** - Automatic orientation switching between horizontal and vertical based on content and space

**Data structure (CheckboxOption):**
- **Core properties** - `label` (required), `value` (required), `helpText` (optional)
- **State control** - `disabled` for individual option control
- **Extensible** - Supports additional properties for custom implementations

**Selection management:**
- **Controlled state** - Use `value` prop (string array) with `onChange` callback for external state management
- **Select All behavior** - When enabled, automatically manages individual checkbox states and provides bulk selection
- **Partial selection** - Select All checkbox shows indeterminate state when some but not all options are selected
- **Individual control** - Each checkbox can be disabled independently while maintaining group behavior

**Layout optimization:**
- **Automatic orientation** - Component calculates optimal layout based on label length and available space
- **Force override** - `cosmosUseWithCaution_forceOptionOrientation` prop for exceptional cases requiring specific layout
- **Responsive behavior** - Layout adapts to container width and content requirements

### Usability

**Option organization:**
- **Logical grouping** - Organize related options that users would naturally consider together
- **Meaningful labels** - Use clear, specific labels that describe each option without ambiguity
- **Consistent terminology** - Maintain consistent language patterns across similar checkbox groups
- **Appropriate ordering** - Arrange options in logical order (alphabetical, frequency of use, or workflow sequence)

**Select All functionality:**
- **Clear purpose** - Only enable Select All when users would realistically want to select all options
- **Obvious behavior** - Ensure it's clear that Select All affects all visible options
- **Partial states** - Users should understand when some options are selected via visual feedback
- **Logical placement** - Select All appears at the top of the option list for clear hierarchy

**Layout considerations:**
- **Natural flow** - Allow automatic orientation to optimize for readability and space usage
- **Scanning patterns** - Horizontal layout works well for short labels; vertical for longer descriptions
- **Touch targets** - Ensure adequate spacing between options for touch interaction
- **Visual hierarchy** - Maintain clear relationship between group label and individual options

### Content

**Group labeling:**
- **Descriptive headers** - Use clear group labels that explain what users are selecting
- **Question format** - Frame as a question when appropriate (e.g., "Which features do you want to enable?")
- **Contextual help** - Provide help text that explains the purpose or impact of the selections
- **Required indicators** - Clearly mark when at least one selection is required

**Option labels:**
- **Specific descriptions** - Use precise labels that clearly differentiate between options
- **Positive phrasing** - Frame options positively rather than as negatives when possible
- **Consistent structure** - Use parallel phrasing across options (e.g., all start with verbs)
- **Appropriate length** - Keep labels concise while providing sufficient context for decision-making

**Help text usage:**
- **Clarifying information** - Use individual option help text to explain complex or technical options
- **Impact description** - Explain what happens when an option is selected
- **Consistent formatting** - Maintain consistent help text patterns across similar groups
- **Complementary content** - Ensure help text adds value rather than repeating the label

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper fieldset/legend grouping and individual checkbox labeling
- Full keyboard navigation support including Tab navigation and Space key activation for each checkbox
- Screen reader announcements for group context, individual selections, and Select All state changes
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators and logical tab order through all checkboxes
- Touch target sizing that meets accessibility guidelines with proper spacing between interactive elements

**Development responsibilities:**
- Provide descriptive group labels that give context for the entire set of options
- Ensure individual option labels are meaningful and distinguishable for screen reader users
- Use appropriate help text that provides necessary context without overwhelming users
- Handle validation errors with clear, actionable feedback that's announced to screen readers
- Ensure the `name` prop creates proper form association for all checkboxes in the group
- Provide stable `value` arrays that don't cause unnecessary re-renders or focus loss

**Design responsibilities:**
- Provide sufficient color contrast for all checkbox states, labels, and help text across different themes
- Design clear visual hierarchy that shows the relationship between group label and individual options
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for similar checkbox groups across the application
- Design appropriate spacing and sizing for touch targets and content density across screen sizes
- Ensure Select All checkbox is visually distinct but clearly part of the same group as individual options