import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Source,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as CheckboxFieldStories from './CheckboxField.stories';

<Meta of={CheckboxFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<Description />

<Primary />

<Controls of={CheckboxFieldStories.Playground} />

## Import

```jsx
import { CheckboxField } from '@drata/cosmos-checkbox-field';
```
## Props

### `defaultChecked`

The `defaultChecked` prop defines the initial `checked` state of the component.

<Canvas of={CheckboxFieldStories.DefaultChecked} />

## 🟢 When to use the component

- **Binary Options**: When presenting users with a simple "yes" or "no" choice. For example, in a survey asking if the user wants to receive promotional emails, a checkbox can be used to let them opt in or out.
- **Multiple Selections**: When users need to select one or more options from a list of choices. For instance, in a data table, users might want to select multiple rows of data to take action on.
- **Settings and Preferences**: Checkboxes are commonly used in settings panels or preference screens where users can enable or disable certain features or customize their experience. For example, allowing users to choose whether to receive notifications or not in a messaging app.
- **Filtering and Sorting**: Where users need to filter or sort data, checkboxes can be used to let them specify criteria. For example, in Controls, users might want to filter by frameworks or approval statuses.
- **Task Lists**: In to-do lists or task management scenarios, checkboxes can be used to mark tasks as completed. This gives users a clear visual indication of their progress and helps them stay organized.

## ❌ When not to use the component

- **Single Option Selection**: If users can only choose one option from a list, a RadioField might be more appropriate than checkboxes. Radio buttons are better suited for mutually exclusive choices, while checkboxes allow for multiple selections.
- **Limited Screen Space**: In designs with limited screen space, checkboxes can take up more room, especially if there are many options. In such cases, SelectField, ComboboxField might be more space-efficient alternatives.

## 🛠️ How it works

### Usability

- **Make the label selectable.** Users should be able to select either the text label or the checkbox to select or deselect an option.
- **Use adequate touch targets.** Make sure selections are adequately spaced for touch screens.
- **Use a logical order.** Make sure the selection options are organized in a meaningful way, like alphabetical or most-frequent to least-frequent. This helps users easily find the option they’re looking for.

### Content

- **Use positive statements.** Negative language in labels can be counterintuitive. For example, use “I want to receive a promotional email” instead of “I don’t want to receive a promotional email.”
- **Use logical labels.** Make sure that the label makes both states — checked and unchecked — clear to the user. If that’s not possible, consider using a RadioField with two individual options instead. Then both states can have their own clearly marked label.
- **Logical Grouping**: If the checkboxes represent related options, consider grouping them together logically to make it easier for users to understand their relationship. Use headings, borders, or whitespace to visually separate different groups of checkboxes.


### Accessibility

This component is built using the [Radix Primitives Checkbox](https://www.radix-ui.com/primitives/docs/components/checkbox), and follows the [tri-state Checkbox WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/checkbox).

- **Use a fieldset and legend for a checkbox group.** Surround a related set of checkboxes with a `<fieldset>`. The `<legend>` provides context for the grouping. Don’t use fieldset and legend for a single check.
- **Use semantic tags.** Each input should have a semantic tag for the `id` attribute, and its corresponding label should have the same value in its `for` attribute.

#### Keyboard Support

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <th>
                <kbd>Space</kbd>
            </th>
            <th>Checks/unchecks the checkbox</th>
        </tr>
    </tbody>
</table>
